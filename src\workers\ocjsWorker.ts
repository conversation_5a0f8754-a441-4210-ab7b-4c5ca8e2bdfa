// Enhanced OpenCascade.js Worker with Advanced Sweep Operations
// Implements comprehensive sweep and boolean cut system according to CAM guide
// Version 3.0 - Enhanced Sweep Operations: 2025-07-13
import type { CNCTool, DrawCommand } from '../types'

// Worker message types - Enhanced with new sweep operations
export interface OCJSWorkerMessage {
  id: string
  type: 'ping' | 'createDoorBody' | 'createToolGeometry' | 'performSweepOperation' | 'exportGLB' | 'createToolBRep' | 'createAllToolBReps' | 'createPositionedToolShapes' | 'createSimpleBoxGLB' | 'testPolylineSweep' | 'createAdvancedSweep' | 'createToolPath' | 'performPipeShellSweep' | 'runComprehensiveTests' | 'getPerformanceStats' | 'getMemoryStats' | 'testWireCreation'
  data: any
}

export interface OCJSWorkerResponse {
  id: string
  type: 'success' | 'error'
  data?: any
  error?: string
}

// Door body creation parameters
export interface DoorBodyParams {
  width: number
  height: number
  thickness: number
  cornerRadius?: number
  offsetX?: number
  offsetY?: number
}

// Enhanced tool path parameters for advanced sweep operations
export interface ToolPathParams {
  points: Array<{ x: number; y: number; z: number }>
  pathType: 'line' | 'arc' | 'spline' | 'polyline'
  isClosed?: boolean
  tangents?: Array<{ x: number; y: number; z: number }>
}

// Advanced sweep operation parameters
export interface AdvancedSweepParams {
  toolShape: string | any  // Tool BRep shape ID or shape object
  toolPath: ToolPathParams  // Tool path definition
  doorBodyShape: string | any  // Door body shape ID or shape object
  operation: 'subtract' | 'union' | 'intersect'
  sweepMode: 'frenet' | 'corrected_frenet' | 'fixed' | 'binormal'
  startPosition?: { x: number; y: number; z: number }
  endPosition?: { x: number; y: number; z: number }
}

// Tool geometry creation parameters
export interface ToolGeometryParams {
  tool: CNCTool
  commands: DrawCommand[]
  depth: number
  isBottomFace: boolean
  doorWidth?: number
  doorHeight?: number
}

// Sweep operation parameters
export interface SweepOperationParams {
  doorBodyShape: any // OC shape reference
  toolGeometries: any[] // Array of OC tool shapes
  operation: 'subtract' | 'union' | 'intersect'
}

// Tool BRep creation parameters
export interface ToolBRepParams {
  tool: CNCTool
  height?: number
  includeGLB?: boolean
}

// All tools BRep creation parameters
export interface AllToolBRepsParams {
  tools: CNCTool[]
  height?: number
  includeGLB?: boolean
}

// Test polyline sweep parameters
export interface TestPolylineSweepParams {
  polylinePoints: { x: number; y: number; z: number }[]
  profileShape: string // Shape ID
  doorBodyShape: string // Shape ID
}

let oc: any = null
let isInitialized = false

// Enhanced shape cache with memory tracking
const shapeCache = new Map<string, any>()
const toolCache = new Map<string, any>()

// Memory management utilities for OCJS objects
class OCJSMemoryManager {
  private static instance: OCJSMemoryManager
  private trackedObjects: Map<string, any[]> = new Map()
  private cleanupCallbacks: Array<() => void> = []

  public static getInstance(): OCJSMemoryManager {
    if (!OCJSMemoryManager.instance) {
      OCJSMemoryManager.instance = new OCJSMemoryManager()
    }
    return OCJSMemoryManager.instance
  }

  // Track objects for later cleanup
  public trackObjects(operationId: string, objects: any[]): void {
    if (!this.trackedObjects.has(operationId)) {
      this.trackedObjects.set(operationId, [])
    }
    this.trackedObjects.get(operationId)!.push(...objects)
  }

  // Clean up objects for a specific operation
  public cleanupOperation(operationId: string): void {
    const objects = this.trackedObjects.get(operationId)
    if (objects) {
      let cleanedCount = 0
      objects.forEach(obj => {
        try {
          if (obj && typeof obj.delete === 'function') {
            obj.delete()
            cleanedCount++
          }
        } catch (error) {
          console.warn('⚠️ Memory cleanup warning:', error)
        }
      })
      this.trackedObjects.delete(operationId)
      console.log(`🧹 Cleaned up ${cleanedCount} objects for operation: ${operationId}`)
    }
  }

  // Clean up all tracked objects
  public cleanupAll(): void {
    let totalCleaned = 0
    for (const [, objects] of this.trackedObjects) {
      objects.forEach(obj => {
        try {
          if (obj && typeof obj.delete === 'function') {
            obj.delete()
            totalCleaned++
          }
        } catch (error) {
          console.warn('⚠️ Memory cleanup warning:', error)
        }
      })
    }
    this.trackedObjects.clear()

    // Execute cleanup callbacks
    this.cleanupCallbacks.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.warn('⚠️ Cleanup callback error:', error)
      }
    })
    this.cleanupCallbacks = []

    console.log(`🧹 Total memory cleanup: ${totalCleaned} objects deleted`)
  }

  // Register cleanup callback
  public registerCleanupCallback(callback: () => void): void {
    this.cleanupCallbacks.push(callback)
  }

  // Get memory usage statistics
  public getMemoryStats(): { trackedOperations: number; totalObjects: number } {
    let totalObjects = 0
    for (const objects of this.trackedObjects.values()) {
      totalObjects += objects.length
    }
    return {
      trackedOperations: this.trackedObjects.size,
      totalObjects: totalObjects
    }
  }
}

const memoryManager = OCJSMemoryManager.getInstance()

// Performance optimization utilities
class OCJSPerformanceOptimizer {
  private static instance: OCJSPerformanceOptimizer
  private operationTimings: Map<string, number[]> = new Map()
  private batchSize = 10 // Default batch size for parallel operations

  public static getInstance(): OCJSPerformanceOptimizer {
    if (!OCJSPerformanceOptimizer.instance) {
      OCJSPerformanceOptimizer.instance = new OCJSPerformanceOptimizer()
    }
    return OCJSPerformanceOptimizer.instance
  }

  // Time an operation
  public timeOperation<T>(operationName: string, operation: () => T): T {
    const startTime = performance.now()
    const result = operation()
    const endTime = performance.now()
    const duration = endTime - startTime

    if (!this.operationTimings.has(operationName)) {
      this.operationTimings.set(operationName, [])
    }
    this.operationTimings.get(operationName)!.push(duration)

    console.log(`⏱️ ${operationName}: ${duration.toFixed(2)}ms`)
    return result
  }

  // Get performance statistics
  public getPerformanceStats(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const stats: Record<string, { avg: number; min: number; max: number; count: number }> = {}

    for (const [operation, timings] of this.operationTimings) {
      const avg = timings.reduce((a, b) => a + b, 0) / timings.length
      const min = Math.min(...timings)
      const max = Math.max(...timings)

      stats[operation] = {
        avg: parseFloat(avg.toFixed(2)),
        min: parseFloat(min.toFixed(2)),
        max: parseFloat(max.toFixed(2)),
        count: timings.length
      }
    }

    return stats
  }

  // Optimize boolean operations with parallel processing
  public async optimizedBooleanOperations(
    baseShape: any,
    toolShapes: any[],
    operation: 'subtract' | 'union' | 'intersect'
  ): Promise<any> {
    console.log(`🚀 Starting optimized ${operation} with ${toolShapes.length} tools`)

    if (toolShapes.length === 0) return baseShape
    if (toolShapes.length === 1) {
      return this.performSingleBooleanOperation(baseShape, toolShapes[0], operation)
    }

    // Batch process for better performance
    let resultShape = baseShape
    const batchSize = Math.min(this.batchSize, toolShapes.length)

    for (let i = 0; i < toolShapes.length; i += batchSize) {
      const batch = toolShapes.slice(i, i + batchSize)
      console.log(`🔧 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(toolShapes.length / batchSize)}`)

      for (const toolShape of batch) {
        resultShape = this.performSingleBooleanOperation(resultShape, toolShape, operation)
      }
    }

    return resultShape
  }

  // Perform single boolean operation with optimization
  private performSingleBooleanOperation(shape1: any, shape2: any, operation: 'subtract' | 'union' | 'intersect'): any {
    return this.timeOperation(`Boolean_${operation}`, () => {
      let booleanOp: any

      if (operation === 'subtract') {
        booleanOp = new oc.BRepAlgoAPI_Cut_3(shape1, shape2, new oc.Message_ProgressRange_1())
      } else if (operation === 'union') {
        booleanOp = new oc.BRepAlgoAPI_Fuse_3(shape1, shape2, new oc.Message_ProgressRange_1())
      } else if (operation === 'intersect') {
        booleanOp = new oc.BRepAlgoAPI_Common_3(shape1, shape2, new oc.Message_ProgressRange_1())
      } else {
        throw new Error(`Unsupported boolean operation: ${operation}`)
      }

      booleanOp.SetRunParallel(true) // Enable parallel processing
      booleanOp.Build(new oc.Message_ProgressRange_1())

      if (!booleanOp.IsDone() || booleanOp.Shape().IsNull()) {
        booleanOp.delete()
        throw new Error(`Boolean ${operation} operation failed`)
      }

      const result = booleanOp.Shape()
      booleanOp.delete() // Clean up immediately
      return result
    })
  }

  // Optimize tool path generation
  public optimizeToolPath(points: Array<{ x: number; y: number; z: number }>): Array<{ x: number; y: number; z: number }> {
    if (points.length <= 2) return points

    return this.timeOperation('ToolPath_Optimization', () => {
      // Remove duplicate consecutive points
      const optimized = [points[0]]
      const tolerance = 0.001 // 1 micrometer tolerance

      for (let i = 1; i < points.length; i++) {
        const prev = optimized[optimized.length - 1]
        const curr = points[i]

        const distance = Math.sqrt(
          Math.pow(curr.x - prev.x, 2) +
          Math.pow(curr.y - prev.y, 2) +
          Math.pow(curr.z - prev.z, 2)
        )

        if (distance > tolerance) {
          optimized.push(curr)
        }
      }

      console.log(`🎯 Tool path optimized: ${points.length} → ${optimized.length} points`)
      return optimized
    })
  }

  // Set batch size for parallel operations
  public setBatchSize(size: number): void {
    this.batchSize = Math.max(1, size)
    console.log(`⚙️ Batch size set to: ${this.batchSize}`)
  }
}

const performanceOptimizer = OCJSPerformanceOptimizer.getInstance()



// Initialize OpenCascade.js
async function initializeOC() {
  if (!isInitialized) {
    try {
      console.log('🔧 Starting OpenCascade.js initialization...')
      
      // Add timeout for initialization
      const initTimeout = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('OpenCascade.js initialization timeout after 30 seconds'))
        }, 30000)
      })
      
      const initPromise = (async () => {
        // Dynamic import to handle WASM loading properly
        const { default: initOpenCascade } = await import('opencascade.js')
        console.log('🔧 OpenCascade.js module loaded, initializing WASM...')
        oc = await initOpenCascade()
        console.log('✅ OpenCascade.js WASM initialized successfully')
        return oc
      })()
      
      oc = await Promise.race([initPromise, initTimeout])
      isInitialized = true
      console.log('✅ OpenCascade.js initialized in worker')
    } catch (error) {
      console.error('❌ Failed to initialize OpenCascade.js:', error)
      isInitialized = false
      throw error
    }
  }
  return oc
}

// Create door body from PANEL layer data
function createDoorBody(params: DoorBodyParams): any {
  const { width, height, thickness, cornerRadius: _cornerRadius = 0 } = params

  try {
    // For now, create a simple rectangular door body to avoid OpenCascade.js constructor issues
    // TODO: Add rounded corners support once the correct API is determined
    console.log('Creating box with dimensions:', width, height, thickness)

    // Create a simple box
    const box = new oc.BRepPrimAPI_MakeBox_2(width, height, thickness)
    console.log(`🚪 Door body dimensions: W=${width}m, H=${height}m, T=${thickness}m`)

    // Position the box: centered in X,Y but from Z=0 down to Z=-thickness
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(-width/2, -height/2, -thickness))
    const loc = new oc.TopLoc_Location_2(tf)
    const positionedBox = box.Shape().Moved(loc, false)
    console.log(`🚪 Door body positioned: X=[-${width/2}, ${width/2}], Y=[-${height/2}, ${height/2}], Z=[0, -${thickness}]`)

    // Cache the door body shape
    const shapeId = `door_${Date.now()}`
    shapeCache.set(shapeId, positionedBox)
    console.log('✅ Door body cached with ID:', shapeId)

    return {
      success: true,
      shapeId: shapeId,
      dimensions: { width, height, thickness }
    }
  } catch (error) {
    console.error('Error creating door body:', error)
    throw error
  }
}

// Enhanced tool geometry creation with proper BRep generation
function createToolGeometry(params: ToolGeometryParams): any {
  const { tool, commands, depth, isBottomFace, doorWidth, doorHeight } = params

  try {
    console.log(`🔧 Creating enhanced tool geometry for ${tool.name} (${tool.shape})`)
    const toolGeometries: any[] = []
    // const createdObjects: any[] = [] // Track objects for cleanup

    commands.forEach((command, index) => {
      let toolShape: any = null

      try {
        // Create tool shape based on type with enhanced BRep generation
        toolShape = createEnhancedToolBRep(tool, depth)

        if (toolShape) {
          // Position tool based on command coordinates with proper coordinate conversion
          const positionedTool = positionToolAtCommand(toolShape, command, depth, isBottomFace, doorWidth, doorHeight)
          if (positionedTool) {
            toolGeometries.push(positionedTool)
            console.log(`✅ Tool ${index} positioned successfully`)
          }
        }
      } catch (toolError) {
        console.error(`❌ Error creating tool ${index}:`, toolError)
        // Continue with other tools even if one fails
      }
    })

    // Store geometries in cache with proper IDs
    const geometryIds = toolGeometries.map((geometry, index) => {
      const toolId = `enhanced_tool_${tool.name}_${Date.now()}_${index}`
      toolCache.set(toolId, geometry)
      return toolId
    })

    console.log(`✅ Created ${toolGeometries.length} enhanced tool geometries`)

    return {
      count: toolGeometries.length,
      success: true,
      geometryIds: geometryIds
    }
  } catch (error) {
    console.error('❌ Error in enhanced tool geometry creation:', error)
    throw error
  }
}

// Create enhanced tool BRep with proper geometry according to tool type
function createEnhancedToolBRep(tool: CNCTool, height: number): any {
  const radius = tool.diameter / 2000 // Convert mm to meters
  const toolHeight = height

  console.log(`🔧 Creating ${tool.shape} tool BRep: diameter=${tool.diameter}mm, height=${height*1000}mm`)

  try {
    let toolShape: any = null
    const objectsToCleanup: any[] = []

    if (tool.shape === 'cylindrical') {
      // Create cylindrical endmill with proper axis orientation
      const axis = new oc.gp_Ax2_2(
        new oc.gp_Pnt_3(0, 0, 0),
        new oc.gp_Dir_4(0, 0, 1)
      )
      objectsToCleanup.push(axis)

      const cylinder = new oc.BRepPrimAPI_MakeCylinder_3(axis, radius, toolHeight)
      toolShape = cylinder.Shape()
      objectsToCleanup.push(cylinder)

    } else if (tool.shape === 'conical') {
      // Create V-bit tool with proper cone geometry
      const tipRadius = 0.0005 // 0.5mm tip in meters
      const axis = new oc.gp_Ax2_2(
        new oc.gp_Pnt_3(0, 0, 0),
        new oc.gp_Dir_4(0, 0, 1)
      )
      objectsToCleanup.push(axis)

      const cone = new oc.BRepPrimAPI_MakeCone_4(axis, tipRadius, radius, toolHeight)
      toolShape = cone.Shape()
      objectsToCleanup.push(cone)

    } else if (tool.shape === 'ballnose') {
      // Create ballnose tool with hemisphere + cylinder
      const cylinderHeight = toolHeight - radius

      // Create cylinder part
      const cylinderAxis = new oc.gp_Ax2_2(
        new oc.gp_Pnt_3(0, 0, radius),
        new oc.gp_Dir_4(0, 0, 1)
      )
      objectsToCleanup.push(cylinderAxis)

      const cylinder = new oc.BRepPrimAPI_MakeCylinder_3(cylinderAxis, radius, cylinderHeight)
      objectsToCleanup.push(cylinder)

      // Create hemisphere part
      const sphereCenter = new oc.gp_Pnt_3(0, 0, 0)
      const sphere = new oc.BRepPrimAPI_MakeSphere_2(sphereCenter, radius)
      objectsToCleanup.push(sphere)

      // Create hemisphere by cutting sphere in half
      const cuttingPlane = new oc.gp_Pln_3(
        new oc.gp_Pnt_3(0, 0, 0),
        new oc.gp_Dir_4(0, 0, 1)
      )
      objectsToCleanup.push(cuttingPlane)

      const halfSpace = new oc.BRepPrimAPI_MakeHalfSpace(
        new oc.BRepBuilderAPI_MakeFace_2(cuttingPlane).Face(),
        new oc.gp_Pnt_3(0, 0, -1)
      )
      objectsToCleanup.push(halfSpace)

      const hemispherecut = new oc.BRepAlgoAPI_Cut_3(
        sphere.Shape(),
        halfSpace.Shape(),
        new oc.Message_ProgressRange_1()
      )
      hemispherecut.Build(new oc.Message_ProgressRange_1())
      objectsToCleanup.push(hemispherecut)

      // Fuse cylinder and hemisphere
      const fuse = new oc.BRepAlgoAPI_Fuse_3(
        cylinder.Shape(),
        hemispherecut.Shape(),
        new oc.Message_ProgressRange_1()
      )
      fuse.Build(new oc.Message_ProgressRange_1())
      toolShape = fuse.Shape()
      objectsToCleanup.push(fuse)

    } else {
      throw new Error(`Unsupported tool shape: ${tool.shape}`)
    }

    // Clean up intermediate objects to prevent memory leaks
    objectsToCleanup.forEach(obj => {
      try {
        if (obj && typeof obj.delete === 'function') {
          obj.delete()
        }
      } catch (cleanupError) {
        console.warn('⚠️ Cleanup warning:', cleanupError)
      }
    })

    console.log(`✅ Enhanced ${tool.shape} tool BRep created successfully`)
    return toolShape

  } catch (error) {
    console.error(`❌ Error creating enhanced tool BRep:`, error)
    throw error
  }
}

// Position tool at command location with proper coordinate conversion
function positionToolAtCommand(toolShape: any, command: DrawCommand, depth: number, isBottomFace: boolean, doorWidth?: number, doorHeight?: number): any {
  try {
    const tf = new oc.gp_Trsf_1()

    if (command.command_type === 'line') {
      // Position at line midpoint
      const midX = ((command.x1 + command.x2) / 2) / 1000 - (doorWidth || 0) / 2
      const midZ = ((command.y1 + command.y2) / 2) / 1000 - (doorHeight || 0) / 2
      const posY = isBottomFace ? -depth : 0
      console.log(`🔧 Positioning line tool at: X=${midX*1000}mm, Y=${posY*1000}mm, Z=${midZ*1000}mm`)
      tf.SetTranslation_1(new oc.gp_Vec_4(midX, posY, midZ))

    } else if (command.command_type === 'circle') {
      // Position at circle center
      const centerX = (command.x1 / 1000) - (doorWidth || 0) / 2
      const centerZ = (command.y1 / 1000) - (doorHeight || 0) / 2
      const posY = isBottomFace ? -depth : 0
      console.log(`🔧 Positioning circle tool at: X=${centerX*1000}mm, Y=${posY*1000}mm, Z=${centerZ*1000}mm`)
      tf.SetTranslation_1(new oc.gp_Vec_4(centerX, posY, centerZ))

    } else if (command.command_type === 'rectangle') {
      // Position at rectangle center
      const centerX = ((command.x1 + command.x2) / 2) / 1000 - (doorWidth || 0) / 2
      const centerZ = ((command.y1 + command.y2) / 2) / 1000 - (doorHeight || 0) / 2
      const posY = isBottomFace ? -depth : 0
      console.log(`🔧 Positioning rectangle tool at: X=${centerX*1000}mm, Y=${posY*1000}mm, Z=${centerZ*1000}mm`)
      tf.SetTranslation_1(new oc.gp_Vec_4(centerX, posY, centerZ))

    } else if (command.command_type === 'polyline' && command.points && command.points.length > 0) {
      // Position at first point of polyline
      const firstPoint = command.points[0]
      // Handle both array format [x, y] and object format {x, y}
      let pointX, pointY
      if (Array.isArray(firstPoint)) {
        pointX = firstPoint[0]
        pointY = firstPoint[1]
      } else if (typeof firstPoint === 'object' && 'x' in firstPoint) {
        pointX = (firstPoint as any).x
        pointY = (firstPoint as any).y
      } else {
        console.warn('⚠️ Invalid polyline point format')
        return null
      }

      const centerX = (pointX / 1000) - (doorWidth || 0) / 2
      const centerZ = (pointY / 1000) - (doorHeight || 0) / 2
      const posY = isBottomFace ? -depth : 0
      console.log(`🔧 Positioning polyline tool at: X=${centerX*1000}mm, Y=${posY*1000}mm, Z=${centerZ*1000}mm`)
      tf.SetTranslation_1(new oc.gp_Vec_4(centerX, posY, centerZ))

    } else {
      console.warn(`⚠️ Unknown command type: ${command.command_type}`)
      return null
    }

    const loc = new oc.TopLoc_Location_2(tf)
    const positionedTool = toolShape.Moved(loc, false)

    // Clean up transformation objects
    tf.delete()
    loc.delete()

    return positionedTool

  } catch (error) {
    console.error(`❌ Error positioning tool:`, error)
    return null
  }
}

// Advanced sweep operation using BRepOffsetAPI_MakePipeShell (as per CAM guide)
function performAdvancedSweep(params: AdvancedSweepParams): any {
  const { toolShape, toolPath, doorBodyShape, operation, sweepMode = 'frenet' } = params

  try {
    console.log(`🔧 Starting advanced sweep operation with BRepOffsetAPI_MakePipeShell`)
    console.log(`📐 Tool path: ${toolPath.pathType}, ${toolPath.points.length} points`)
    console.log(`🔄 Sweep mode: ${sweepMode}`)

    // Get door body shape
    let doorShape: any = null
    if (typeof doorBodyShape === 'string') {
      doorShape = shapeCache.get(doorBodyShape)
      if (!doorShape) {
        throw new Error(`Door body shape not found: ${doorBodyShape}`)
      }
    } else {
      doorShape = doorBodyShape
    }

    // Get tool shape
    let tool: any = null
    if (typeof toolShape === 'string') {
      tool = toolCache.get(toolShape) || shapeCache.get(toolShape)
      if (!tool) {
        throw new Error(`Tool shape not found: ${toolShape}`)
      }
    } else {
      tool = toolShape
    }

    // Step 1: Create tool path wire (spine)
    console.log(`🔧 Creating tool path wire from ${toolPath.points.length} points`)
    const pathWire = createToolPathWire(toolPath)
    if (!pathWire) {
      throw new Error('Failed to create tool path wire')
    }

    // Step 2: Create swept volume using BRepOffsetAPI_MakePipeShell
    console.log(`🔧 Creating swept volume using BRepOffsetAPI_MakePipeShell`)
    const pipeMaker = new oc.BRepOffsetAPI_MakePipeShell(pathWire)

    // Add tool shape as profile to be swept along the path
    pipeMaker.Add(tool, false, false)

    // Set sweep mode for better control
    if (sweepMode === 'frenet') {
      pipeMaker.SetMode_2(true) // Frenet mode
    } else if (sweepMode === 'corrected_frenet') {
      pipeMaker.SetMode_3(new oc.gp_Dir_4(0, 0, 1), false) // Corrected Frenet with Z-up
    }

    // Build the swept shape
    pipeMaker.Build(new oc.Message_ProgressRange_1())

    if (!pipeMaker.IsDone()) {
      throw new Error('Pipe shell creation failed')
    }

    // Make it a solid
    pipeMaker.MakeSolid()
    const sweptVolume = pipeMaker.Shape()

    console.log(`✅ Swept volume created successfully`)

    // Step 3: Perform boolean operation (cut/union/intersect)
    console.log(`🔧 Performing boolean ${operation} operation`)
    let resultShape: any = null

    if (operation === 'subtract') {
      const cutOperation = new oc.BRepAlgoAPI_Cut_3(
        doorShape,
        sweptVolume,
        new oc.Message_ProgressRange_1()
      )
      cutOperation.SetRunParallel(true)
      cutOperation.Build(new oc.Message_ProgressRange_1())

      if (cutOperation.IsDone() && !cutOperation.Shape().IsNull()) {
        resultShape = cutOperation.Shape()
        console.log(`✅ Boolean cut operation completed`)
      } else {
        throw new Error('Boolean cut operation failed')
      }

      cutOperation.delete()

    } else if (operation === 'union') {
      const fuseOperation = new oc.BRepAlgoAPI_Fuse_3(
        doorShape,
        sweptVolume,
        new oc.Message_ProgressRange_1()
      )
      fuseOperation.SetRunParallel(true)
      fuseOperation.Build(new oc.Message_ProgressRange_1())

      if (fuseOperation.IsDone() && !fuseOperation.Shape().IsNull()) {
        resultShape = fuseOperation.Shape()
        console.log(`✅ Boolean fuse operation completed`)
      } else {
        throw new Error('Boolean fuse operation failed')
      }

      fuseOperation.delete()

    } else if (operation === 'intersect') {
      const intersectOperation = new oc.BRepAlgoAPI_Common_3(
        doorShape,
        sweptVolume,
        new oc.Message_ProgressRange_1()
      )
      intersectOperation.SetRunParallel(true)
      intersectOperation.Build(new oc.Message_ProgressRange_1())

      if (intersectOperation.IsDone() && !intersectOperation.Shape().IsNull()) {
        resultShape = intersectOperation.Shape()
        console.log(`✅ Boolean intersect operation completed`)
      } else {
        throw new Error('Boolean intersect operation failed')
      }

      intersectOperation.delete()
    }

    // Clean up intermediate objects
    pipeMaker.delete()
    pathWire.delete()

    // Cache result
    const resultId = `advanced_sweep_${Date.now()}`
    shapeCache.set(resultId, resultShape)

    console.log(`✅ Advanced sweep operation completed successfully`)

    return {
      success: true,
      shapeId: resultId,
      operation: operation,
      sweepMode: sweepMode,
      pathPoints: toolPath.points.length
    }

  } catch (error) {
    console.error('❌ Error in advanced sweep operation:', error)
    throw error
  }
}

// Create tool path wire from path parameters
function createToolPathWire(toolPath: ToolPathParams): any {
  try {
    console.log(`🔧 Creating ${toolPath.pathType} wire with ${toolPath.points.length} points`)

    if (toolPath.points.length < 2) {
      throw new Error('Tool path must have at least 2 points')
    }

    const wireBuilder = new oc.BRepBuilderAPI_MakeWire_1()
    const objectsToCleanup: any[] = []

    if (toolPath.pathType === 'line' || toolPath.pathType === 'polyline') {
      // Create edges between consecutive points
      for (let i = 0; i < toolPath.points.length - 1; i++) {
        const p1 = toolPath.points[i]
        const p2 = toolPath.points[i + 1]

        const point1 = new oc.gp_Pnt_3(p1.x / 1000, p1.y / 1000, p1.z / 1000) // Convert mm to meters
        const point2 = new oc.gp_Pnt_3(p2.x / 1000, p2.y / 1000, p2.z / 1000)
        objectsToCleanup.push(point1, point2)

        // Create vertices from points
        const vertex1 = new oc.BRepBuilderAPI_MakeVertex(point1).Vertex()
        const vertex2 = new oc.BRepBuilderAPI_MakeVertex(point2).Vertex()
        objectsToCleanup.push(vertex1, vertex2)

        // Create edge from vertices
        const edge = new oc.BRepBuilderAPI_MakeEdge_2(vertex1, vertex2)
        objectsToCleanup.push(edge)

        if (!edge.IsDone()) {
          throw new Error(`Failed to create edge between points ${i} and ${i + 1}`)
        }
        wireBuilder.Add_2(edge.Edge())
      }

      // Close the wire if specified
      if (toolPath.isClosed && toolPath.points.length > 2) {
        const firstPoint = toolPath.points[0]
        const lastPoint = toolPath.points[toolPath.points.length - 1]

        const p1 = new oc.gp_Pnt_3(lastPoint.x / 1000, lastPoint.y / 1000, lastPoint.z / 1000)
        const p2 = new oc.gp_Pnt_3(firstPoint.x / 1000, firstPoint.y / 1000, firstPoint.z / 1000)
        objectsToCleanup.push(p1, p2)

        // Create vertices from points
        const vertex1 = new oc.BRepBuilderAPI_MakeVertex(p1).Vertex()
        const vertex2 = new oc.BRepBuilderAPI_MakeVertex(p2).Vertex()
        objectsToCleanup.push(vertex1, vertex2)

        // Create closing edge from vertices
        const closingEdge = new oc.BRepBuilderAPI_MakeEdge_2(vertex1, vertex2)
        objectsToCleanup.push(closingEdge)

        if (!closingEdge.IsDone()) {
          throw new Error('Failed to create closing edge')
        }
        wireBuilder.Add_2(closingEdge.Edge())
      }

    } else if (toolPath.pathType === 'spline') {
      // Create B-spline curve through points
      const pointArray = new oc.TColgp_Array1OfPnt_2(1, toolPath.points.length)
      objectsToCleanup.push(pointArray)

      toolPath.points.forEach((point, index) => {
        const ocPoint = new oc.gp_Pnt_3(point.x / 1000, point.y / 1000, point.z / 1000)
        pointArray.SetValue(index + 1, ocPoint)
        objectsToCleanup.push(ocPoint)
      })

      const splineBuilder = new oc.GeomAPI_PointsToBSpline_2(pointArray)
      objectsToCleanup.push(splineBuilder)

      // Try using the default constructor and then setting the curve
      const splineEdge = new oc.BRepBuilderAPI_MakeEdge_1()
      splineEdge.Init_1(splineBuilder.Curve())
      objectsToCleanup.push(splineEdge)

      if (!splineEdge.IsDone()) {
        throw new Error('Failed to create spline edge')
      }
      wireBuilder.Add_2(splineEdge.Edge())

    } else if (toolPath.pathType === 'arc') {
      // Create arc from 3 points (start, middle, end)
      if (toolPath.points.length < 3) {
        throw new Error('Arc path requires at least 3 points')
      }

      const p1 = new oc.gp_Pnt_3(
        toolPath.points[0].x / 1000,
        toolPath.points[0].y / 1000,
        toolPath.points[0].z / 1000
      )
      const p2 = new oc.gp_Pnt_3(
        toolPath.points[1].x / 1000,
        toolPath.points[1].y / 1000,
        toolPath.points[1].z / 1000
      )
      const p3 = new oc.gp_Pnt_3(
        toolPath.points[2].x / 1000,
        toolPath.points[2].y / 1000,
        toolPath.points[2].z / 1000
      )
      objectsToCleanup.push(p1, p2, p3)

      const arc = new oc.GC_MakeArcOfCircle_4(p1, p2, p3)
      objectsToCleanup.push(arc)

      // Try using the default constructor and then setting the curve
      const arcEdge = new oc.BRepBuilderAPI_MakeEdge_1()
      arcEdge.Init_1(arc.Value())
      objectsToCleanup.push(arcEdge)

      if (!arcEdge.IsDone()) {
        throw new Error('Failed to create arc edge')
      }
      wireBuilder.Add_2(arcEdge.Edge())
    }

    if (!wireBuilder.IsDone()) {
      throw new Error('Failed to build wire from tool path')
    }

    const wire = wireBuilder.Wire()

    // Clean up intermediate objects
    objectsToCleanup.forEach(obj => {
      try {
        if (obj && typeof obj.delete === 'function') {
          obj.delete()
        }
      } catch (cleanupError) {
        console.warn('⚠️ Wire cleanup warning:', cleanupError)
      }
    })

    console.log(`✅ Tool path wire created successfully`)
    return wire

  } catch (error) {
    console.error('❌ Error creating tool path wire:', error)
    throw error
  }
}

// Helper function to determine operation type from layer name
function determineOperationFromLayerName(layerName: string): string {
  const layer = layerName.toLowerCase()

  if (layer.includes('drill') || layer.includes('delme')) {
    return 'drilling'
  } else if (layer.includes('pocket') || layer.includes('cep')) {
    return 'pocketing'
  } else if (layer.includes('k_') || layer.includes('groove') || layer.includes('kanal')) {
    return 'grooving'
  } else if (layer.includes('h_') || layer.includes('contour')) {
    return 'profiling'
  } else if (layer.includes('v') && (layer.includes('acili') || layer.includes('chamfer'))) {
    return 'chamfering'
  } else if (layer.includes('radius') || layer.includes('fillet')) {
    return 'filleting'
  }

  return 'profiling' // Default operation
}

// Helper function to create circular groove shape (annular cylinder for surface groove)
function createCircularGrooveShape(centerX: number, centerY: number, grooveRadius: number, toolRadius: number, height: number): any {
  try {
    // Create an annular cylinder (ring) that represents the groove cut
    // This creates a cylindrical groove on the surface, not a torus
    const outerRadius = grooveRadius + toolRadius / 2
    const innerRadius = grooveRadius - toolRadius / 2

    // Ensure inner radius is positive
    const actualInnerRadius = Math.max(innerRadius, toolRadius / 4)

    console.log(`🔧 Creating groove: outer=${outerRadius*1000}mm, inner=${actualInnerRadius*1000}mm, height=${height*1000}mm`)

    // Create outer cylinder
    const outerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(outerRadius, height)

    // Create inner cylinder to subtract
    const innerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(actualInnerRadius, height)

    // Subtract inner from outer to create annular groove
    const cut = new oc.BRepAlgoAPI_Cut_3(
      outerCylinder.Shape(),
      innerCylinder.Shape(),
      new oc.Message_ProgressRange_1()
    )
    cut.Build(new oc.Message_ProgressRange_1())

    // Position the groove at the correct location
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, 0))
    const loc = new oc.TopLoc_Location_2(tf)

    return cut.Shape().Moved(loc, false)
  } catch (error) {
    console.error('Error creating groove shape:', error)
    throw new Error(`Failed to create circular groove shape: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Helper function to create pocket shape from spiral toolpath
function createPocketShape(centerX: number, centerY: number, circleRadius: number, toolRadius: number, height: number): any {
  try {
    // For now, create a simple cylinder that represents the pocketed area
    // TODO: Implement proper spiral toolpath shape generation
    const pocketRadius = circleRadius - toolRadius / 2
    const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(pocketRadius, height)

    // Position the cylinder at the center
    const tf = new oc.gp_Trsf_1()
    tf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, 0))
    const loc = new oc.TopLoc_Location_2(tf)

    return cylinder.Shape().Moved(loc, false)
  } catch (error) {
    console.error('Error creating pocket shape:', error)
    throw new Error(`Failed to create pocket shape: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Create positioned tool shapes for each command
function createPositionedToolShapes(params: {
  tool: CNCTool
  commands: DrawCommand[]
  depth: number
  isBottomFace: boolean
  doorWidth: number
  doorHeight: number
  doorOffsetX?: number
  doorOffsetY?: number
}): any {
  const { tool, commands, depth, isBottomFace, doorWidth, doorHeight, doorOffsetX = 0, doorOffsetY = 0 } = params

  try {
    const positionedShapes: any[] = []
    const shapeIds: string[] = []
    // Convert tool diameter from mm to meters
    const radius = (tool.diameter / 1000) / 2
    console.log(`🔧 Tool ${tool.name}: diameter=${tool.diameter}mm, radius=${radius}m (${radius*1000}mm)`)

    console.log(`🔧 Creating ${commands.length} positioned ${tool.shape} tool shapes for ${tool.name}`)

    commands.forEach((command, index) => {
      let toolShape: any = null

      // Create base tool shape with appropriate height based on operation
      const doorThickness = 0.02 // 20mm door thickness in meters

      // Determine operation type first to set correct tool height
      const layerName = command.layer_name || tool.name || ''
      const operation = determineOperationFromLayerName(layerName)

      // Set tool height based on operation type
      let toolHeight: number
      if (operation === 'drilling') {
        toolHeight = depth // Use actual drilling depth for drilling operations
      } else {
        toolHeight = Math.max(depth, doorThickness * 1.5) // Ensure tool is tall enough for other operations
      }

      switch (tool.shape) {
        case 'cylindrical':
          const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, toolHeight)
          toolShape = cylinder.Shape()
          break

        case 'conical':
          const conicalTool = tool as any
          const tipRadius = (conicalTool.tipDiameter || 0.1) / 2
          const cone = new oc.BRepPrimAPI_MakeCone_1(tipRadius, radius, toolHeight)
          toolShape = cone.Shape()
          break

        case 'ballnose':
          const ballnoseTool = tool as any
          const ballRadius = ballnoseTool.ballRadius || radius
          const cylHeight = Math.max(toolHeight - ballRadius, 0)
          const ballCylinder = new oc.BRepPrimAPI_MakeCylinder_1(ballRadius, cylHeight)
          const hemisphere = new oc.BRepPrimAPI_MakeSphere_1(ballRadius)

          // Position hemisphere at bottom
          const tf = new oc.gp_Trsf_1()
          tf.SetTranslation_1(new oc.gp_Vec_4(0, 0, -ballRadius))
          const loc = new oc.TopLoc_Location_2(tf)
          const movedHemisphere = hemisphere.Shape().Moved(loc, false)

          // Fuse cylinder and hemisphere
          const fuse = new oc.BRepAlgoAPI_Fuse_3(
            ballCylinder.Shape(),
            movedHemisphere,
            new oc.Message_ProgressRange_1()
          )
          fuse.Build(new oc.Message_ProgressRange_1())
          toolShape = fuse.Shape()
          break

        default:
          console.warn(`Unknown tool shape: ${tool.shape}`)
          return
      }

      if (toolShape) {
        // Position tool based on command coordinates
        const positionTf = new oc.gp_Trsf_1()

        if (command.command_type === 'line') {
          // Handle line operations - different operations based on layer name
          const x1_mm = command.x1 || 0
          const y1_mm = command.y1 || 0
          const x2_mm = command.x2 || 0
          const y2_mm = command.y2 || 0

          // Calculate line center and dimensions
          const midX_mm = (x1_mm + x2_mm) / 2
          const midY_mm = (y1_mm + y2_mm) / 2
          const lineLength_mm = Math.sqrt(Math.pow(x2_mm - x1_mm, 2) + Math.pow(y2_mm - y1_mm, 2))
          const lineAngle = Math.atan2(y2_mm - y1_mm, x2_mm - x1_mm)

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          const midX = ((midX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const midY = ((midY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000

          // Determine operation type from layer name
          const layerName = command.layer_name || tool.name || ''
          const operation = determineOperationFromLayerName(layerName)
          const toolRadius = radius // Tool radius in meters

          console.log(`🔧 Line Layer: ${layerName} → Operation: ${operation}`)

          if (operation === 'drilling') {
            // LINE DRILLING: Use cylindrical tool at line center (like a hole at midpoint)
            console.log(`🔧 LINE DRILLING: Using cylindrical tool at line center`)
            // Keep the original cylindrical toolShape (already created above)
            const drillZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(midX, midY, drillZ))

          } else if (operation === 'grooving' || operation === 'groove') {
            // LINE GROOVE: Create elongated groove along the line path
            console.log(`🔧 LINE GROOVE: Creating elongated groove along line path`)

            const lineLength = lineLength_mm / 1000  // Convert to meters
            const grooveWidth = toolRadius * 2 // Groove width based on tool diameter
            const grooveDepth = Math.min(depth, toolRadius) // Shallow groove

            // Create elongated box for the groove, centered at origin
            const grooveBox = new oc.BRepPrimAPI_MakeBox_2(lineLength, grooveWidth, grooveDepth)
            toolShape = grooveBox.Shape()

            // Apply rotation and translation together
            const grooveZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - grooveDepth) // Top face: start at top surface (Z=0), extend down by groove depth
            
            // Create combined transformation: first center the box, then rotate, then translate
            const combinedTf = new oc.gp_Trsf_1()
            
            // First, center the box at origin (box is created from 0,0,0)
            combinedTf.SetTranslation_1(new oc.gp_Vec_4(-lineLength/2, -grooveWidth/2, 0))
            
            // Rotate around origin if needed
            if (Math.abs(lineAngle) > 0.01) {
              console.log(`🔧 LINE GROOVE: Applying rotation ${(lineAngle * 180 / Math.PI).toFixed(1)}°`)
              const rotationTf = new oc.gp_Trsf_1()
              const rotationAxis = new oc.gp_Ax1_2(new oc.gp_Pnt_1(), new oc.gp_Dir_4(0, 0, 1)) // Z-axis
              rotationTf.SetRotation_1(rotationAxis, lineAngle)
              combinedTf.PreMultiply(rotationTf)
            }
            
            // Apply final translation to move to target position
            const translateTf = new oc.gp_Trsf_1()
            translateTf.SetTranslation_1(new oc.gp_Vec_4(midX, midY, grooveZ))
            combinedTf.PreMultiply(translateTf)
            
            // Apply the combined transformation to the shape
            const combinedLoc = new oc.TopLoc_Location_2(combinedTf)
            toolShape = toolShape.Moved(combinedLoc, false)
            
            // Position transformation is identity since we already positioned the shape
            positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, 0))

          } else {
            // LINE POCKET: Create elongated rectangular pocket along line
            console.log(`🔧 LINE POCKET: Creating elongated rectangular pocket`)

            const lineLength = lineLength_mm / 1000  // Convert to meters
            const pocketWidth = toolRadius * 3 // Wider pocket
            const pocketDepth = Math.min(depth, lineLength / 4) // Reasonable pocket depth

            // Create elongated box for the pocket, centered at origin
            const pocketBox = new oc.BRepPrimAPI_MakeBox_2(lineLength, pocketWidth, pocketDepth)
            toolShape = pocketBox.Shape()

            // Apply rotation and translation together
            const pocketZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - pocketDepth) // Top face: start at top surface (Z=0), extend down by pocket depth
            
            // Create combined transformation: first center the box, then rotate, then translate
            const combinedTf = new oc.gp_Trsf_1()
            
            // First, center the box at origin (box is created from 0,0,0)
            combinedTf.SetTranslation_1(new oc.gp_Vec_4(-lineLength/2, -pocketWidth/2, 0))
            
            // Rotate around origin if needed
            if (Math.abs(lineAngle) > 0.01) {
              console.log(`🔧 LINE POCKET: Applying rotation ${(lineAngle * 180 / Math.PI).toFixed(1)}°`)
              const rotationTf = new oc.gp_Trsf_1()
              const rotationAxis = new oc.gp_Ax1_2(new oc.gp_Pnt_1(), new oc.gp_Dir_4(0, 0, 1)) // Z-axis
              rotationTf.SetRotation_1(rotationAxis, lineAngle)
              combinedTf.PreMultiply(rotationTf)
            }
            
            // Apply final translation to move to target position
            const translateTf = new oc.gp_Trsf_1()
            translateTf.SetTranslation_1(new oc.gp_Vec_4(midX, midY, pocketZ))
            combinedTf.PreMultiply(translateTf)
            
            // Apply the combined transformation to the shape
            const combinedLoc = new oc.TopLoc_Location_2(combinedTf)
            toolShape = toolShape.Moved(combinedLoc, false)
            
            // Position transformation is identity since we already positioned the shape
            positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, 0))
          }

          console.log(`🔧 Line operation: center=(${midX_mm}, ${midY_mm})mm, length=${lineLength_mm.toFixed(1)}mm, angle=${(lineAngle * 180 / Math.PI).toFixed(1)}°, operation=${operation}`)
        } else if (command.command_type === 'circle') {
          // Convert from Lua coordinate system (mm) to centered coordinate system (meters)
          const centerX_mm = command.x1
          const centerY_mm = command.y1
          const circleRadius_mm = command.radius || 0 // Circle radius from DrawCommand

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          const centerX = ((centerX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const centerY = ((centerY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000
          const circleRadius = circleRadius_mm / 1000 // Convert to meters

          // Position tool to cut from the surface into the door
          const doorThickness = 0.02 // 20mm door thickness in meters
          const toolHeight = Math.max(depth, doorThickness * 1.5)

          // For bottom face operations, position tool to start at bottom surface and extend upward
          // For top face operations, position tool to start at top surface and extend downward
          const posZ = isBottomFace ?
            (-doorThickness + toolHeight/2) : // Bottom face: start at bottom surface, center tool
            (0 - toolHeight/2)    // Top face: start at top surface, center tool

          const toolRadius = radius // Tool radius in meters
          console.log(`🔧 Layer: ${layerName} → Operation: ${operation}`)

          if (operation === 'drilling' || circleRadius <= 0) {
            // DRILLING: Single point operation at circle center
            // Use actual drilling depth, not toolHeight
            const actualDrillDepth = depth // Use the specified depth (10mm = 0.01m)
            const drillZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - actualDrillDepth) // Top face: start at top surface (Z=0), extend down by drill depth only
            console.log(`🔧 DRILLING: Positioning drill tool ${index} at center: X=${centerX.toFixed(4)}m, Y=${centerY.toFixed(4)}m, Z=${drillZ.toFixed(4)}m (depth=${actualDrillDepth*1000}mm, doorThickness=${doorThickness*1000}mm, isBottomFace=${isBottomFace})`)
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))

          } else if (operation === 'grooving' || operation === 'groove') {
            // GROOVE: Create circular groove shape around circle perimeter (borderline)
            console.log(`🔧 GROOVE: Creating groove toolpath around circle perimeter (radius=${circleRadius_mm}mm, tool=${toolRadius*1000}mm)`)

            if (circleRadius > toolRadius) {
              // Create shallow circular groove shape (surface cut only)
              const grooveRadius = circleRadius
              const grooveDepth = Math.min(depth, toolRadius) // Limit groove depth to tool radius or specified depth
              toolShape = createCircularGrooveShape(centerX, centerY, grooveRadius, toolRadius, grooveDepth)
              console.log(`🔧 GROOVE: Created circular groove shape at radius ${grooveRadius*1000}mm, depth=${grooveDepth*1000}mm`)

              // Position the groove to start at surface and extend into material
              // For bottom face: start at bottom surface (Z=-doorThickness) and extend upward (positive Z)
              // For top face: start at top surface (Z=0) and extend downward (negative Z)
              const surfaceZ = isBottomFace ?
                (-doorThickness) : // Bottom: start at bottom surface (Z=-doorThickness)
                (0 - grooveDepth)  // Top: start at top surface (Z=0), extend down by groove depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, surfaceZ))
            } else {
              // Circle too small for groove - use drill shape at center
              console.log(`🔧 GROOVE→DRILL: Circle too small for grooving, using drill shape at center`)
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, posZ))
            }

          } else if (operation === 'pocketing' || operation === 'pocket') {
            // POCKET: Create pocket shape to clear entire circle area
            console.log(`🔧 POCKET: Creating pocket shape inside circle (radius=${circleRadius_mm}mm, tool=${toolRadius*1000}mm)`)

            if (circleRadius > toolRadius * 1.5) {
              // Create shallow pocket shape (surface cut only)
              const pocketDepth = Math.min(depth, circleRadius / 2) // Reasonable pocket depth
              toolShape = createPocketShape(centerX, centerY, circleRadius, toolRadius, pocketDepth)
              console.log(`🔧 POCKET: Created pocket shape with radius ${circleRadius*1000}mm, depth=${pocketDepth*1000}mm`)

              // Position the pocket to start at surface and extend into material
              // For bottom face: start at bottom surface (Z=-doorThickness) and extend upward
              // For top face: start at top surface (Z=0) and extend downward
              const surfaceZ = isBottomFace ?
                (-doorThickness) : // Bottom: start at bottom surface (Z=-doorThickness)
                (0 - pocketDepth)  // Top: start at top surface (Z=0), extend down by pocket depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, surfaceZ))
            } else {
              // Circle too small for pocketing - use drill shape at center
              console.log(`🔧 POCKET→DRILL: Circle too small for pocketing, using drill shape at center`)
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, posZ))
            }

          } else {
            // Default operation (profiling/finishing) - drill at center
            console.log(`🔧 DEFAULT: Unknown operation '${operation}', drilling at center`)
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, posZ))
          }

          console.log(`🔧 Circle operation: center=(${centerX_mm}, ${centerY_mm})mm, radius=${circleRadius_mm}mm, tool=${tool.name}(${toolRadius*1000}mm)`)

        } else if (command.command_type === 'polyline') {
          // Handle polyline operations - different operations based on layer name
          const points = (command as any).points || []
          if (points.length >= 2) {
            // Calculate polyline bounding box
            let minX = points[0].x, maxX = points[0].x
            let minY = points[0].y, maxY = points[0].y

            points.forEach((point: any) => {
              minX = Math.min(minX, point.x)
              maxX = Math.max(maxX, point.x)
              minY = Math.min(minY, point.y)
              maxY = Math.max(maxY, point.y)
            })

            const centerX_mm = (minX + maxX) / 2
            const centerY_mm = (minY + maxY) / 2
            const polyWidth_mm = maxX - minX
            const polyHeight_mm = maxY - minY

            // Convert door dimensions from meters to mm for coordinate calculation
            const doorWidth_mm = doorWidth * 1000
            const doorHeight_mm = doorHeight * 1000

            // Account for door offset and center the coordinates, then convert to meters
            const centerX = ((centerX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
            const centerY = ((centerY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000

            // Determine operation type from layer name
            const layerName = command.layer_name || tool.name || ''
            const operation = determineOperationFromLayerName(layerName)
            const toolRadius = radius // Tool radius in meters

            console.log(`🔧 Polyline Layer: ${layerName} → Operation: ${operation}`)
            console.log(`🔧 Polyline Points: ${points.length} points`, points)

            if (operation === 'drilling') {
              // POLYLINE DRILLING: Use cylindrical tool at polyline center
              console.log(`🔧 POLYLINE DRILLING: Using cylindrical tool at polyline center`)
              // Keep the original cylindrical toolShape (already created above)
              const drillZ = isBottomFace ?
                (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))

            } else if (operation === 'grooving' || operation === 'groove') {
              // POLYLINE GROOVE: Create shape following polyline path using efficient capsule approach
              console.log(`🔧 POLYLINE GROOVE: Creating efficient capsule-based groove following polyline path`)
              const startTime = performance.now()

              const grooveDepth = Math.min(depth, toolRadius) // Shallow groove

              try {
                // Use efficient capsule/swept rectangle approach
                let combinedGroove: any = null
                const doorWidth_mm = doorWidth * 1000
                const doorHeight_mm = doorHeight * 1000

                // Create individual groove segments as capsules (cylinder + rounded ends)
                const grooveSegments: any[] = []

                for (let i = 0; i < points.length - 1; i++) {
                  const p1 = points[i]
                  const p2 = points[i + 1]

                  // Convert to door coordinate system
                  const p1X = ((p1.x - doorOffsetX) - doorWidth_mm / 2) / 1000
                  const p1Y = ((p1.y - doorOffsetY) - doorHeight_mm / 2) / 1000
                  const p2X = ((p2.x - doorOffsetX) - doorWidth_mm / 2) / 1000
                  const p2Y = ((p2.y - doorOffsetY) - doorHeight_mm / 2) / 1000

                  // Calculate segment properties
                  const deltaX = p2X - p1X
                  const deltaY = p2Y - p1Y
                  const segmentLength = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

                  if (segmentLength > 0.001) { // Only process segments longer than 1mm
                    // Calculate segment center and orientation
                    const centerX = (p1X + p2X) / 2
                    const centerY = (p1Y + p2Y) / 2
                    const angle = Math.atan2(deltaY, deltaX)

                    // Create a capsule-like shape: cylinder with hemispherical ends
                    // For efficiency, we'll use a rectangular box with rounded corners
                    const boxWidth = segmentLength + (toolRadius * 2) // Add tool radius on each end
                    const boxHeight = toolRadius * 2
                    const boxDepth = grooveDepth

                    // Create the main box
                    const box = new oc.BRepPrimAPI_MakeBox_2(boxWidth, boxHeight, boxDepth)
                    let segmentShape = box.Shape()

                    // Position and orient the box
                    const segmentTf = new oc.gp_Trsf_1()
                    
                    // First translate to center the box at origin
                    segmentTf.SetTranslation_1(new oc.gp_Vec_4(-boxWidth/2, -boxHeight/2, 0))
                    
                    // Then rotate around Z-axis
                    if (Math.abs(angle) > 0.001) {
                      const rotationTf = new oc.gp_Trsf_1()
                      const zDir = new oc.gp_Dir_3(new oc.gp_Vec_4(0, 0, 1))
                      const zAxis = new oc.gp_Ax1_2(new oc.gp_Pnt_3(0, 0, 0), zDir)
                      rotationTf.SetRotation_1(zAxis, angle)
                      segmentTf.Multiply(rotationTf)
                    }
                    
                    // Finally translate to segment center
                    const finalTf = new oc.gp_Trsf_1()
                    const grooveZ = isBottomFace ?
                      (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                      (0 - grooveDepth) // Top face: start at top surface (Z=0), extend down by groove depth
                    finalTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, grooveZ))
                    segmentTf.Multiply(finalTf)

                    const segmentLoc = new oc.TopLoc_Location_2(segmentTf)
                    segmentShape = segmentShape.Moved(segmentLoc, false)
                    
                    grooveSegments.push(segmentShape)
                    console.log(`🔧 POLYLINE GROOVE: Created segment ${i}: length=${segmentLength.toFixed(3)}m, angle=${(angle * 180 / Math.PI).toFixed(1)}°`)
                  }
                }

                // Combine all segments efficiently using a single compound operation
                if (grooveSegments.length > 0) {
                  if (grooveSegments.length === 1) {
                    combinedGroove = grooveSegments[0]
                  } else {
                    // Use compound builder for better performance
                    const compound = new oc.TopoDS_Compound()
                    const builder = new oc.BRep_Builder()
                    builder.MakeCompound(compound)
                    
                    grooveSegments.forEach((segment) => {
                      builder.Add(compound, segment)
                    })
                    
                    combinedGroove = compound
                    console.log(`🔧 POLYLINE GROOVE: Combined ${grooveSegments.length} segments into compound`)
                  }
                }

                if (combinedGroove) {
                  toolShape = combinedGroove
                  positionTf.SetTranslation_1(new oc.gp_Vec_4(0, 0, 0))
                  const endTime = performance.now()
                  console.log(`🔧 POLYLINE GROOVE: Created efficient groove using ${grooveSegments.length} capsule segments in ${(endTime - startTime).toFixed(2)}ms`)
                } else {
                  throw new Error(`Failed to create polyline groove segments - no valid segments generated from ${points.length} points`)
                }
              } catch (error) {
                console.error(`🔧 POLYLINE GROOVE: Capsule method failed: ${error}`)
                throw new Error(`Polyline groove creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
              }

            } else {
              // POLYLINE POCKET: Create pocket covering polyline area
              console.log(`🔧 POLYLINE POCKET: Creating pocket covering polyline area`)

              const polyWidth = polyWidth_mm / 1000  // Convert to meters
              const polyHeight = polyHeight_mm / 1000 // Convert to meters
              const pocketDepth = Math.min(depth, Math.min(polyWidth, polyHeight) / 2) // Reasonable pocket depth

              // Create bounding box pocket
              const pocketBox = new oc.BRepPrimAPI_MakeBox_2(polyWidth, polyHeight, pocketDepth)
              toolShape = pocketBox.Shape()

              // Position the pocket
              const pocketZ = isBottomFace ?
                (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                (0 - pocketDepth) // Top face: start at top surface (Z=0), extend down by pocket depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX - polyWidth/2, centerY - polyHeight/2, pocketZ))
            }

            console.log(`🔧 Polyline operation: center=(${centerX_mm}, ${centerY_mm})mm, bounds=${polyWidth_mm.toFixed(1)}×${polyHeight_mm.toFixed(1)}mm, operation=${operation}`)
          }

        } else if (command.command_type === 'rectangle') {
          // Handle rectangle operations - different operations based on layer name
          const x1_mm = command.x1 || 0
          const y1_mm = command.y1 || 0
          const x2_mm = command.x2 || 0
          const y2_mm = command.y2 || 0

          // Calculate rectangle center and dimensions
          const centerX_mm = (x1_mm + x2_mm) / 2
          const centerY_mm = (y1_mm + y2_mm) / 2
          const rectWidth_mm = Math.abs(x2_mm - x1_mm)
          const rectHeight_mm = Math.abs(y2_mm - y1_mm)

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          const centerX = ((centerX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const centerY = ((centerY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000

          // Determine operation type from layer name (same logic as circles)
          const layerName = command.layer_name || tool.name || ''
          const operation = determineOperationFromLayerName(layerName)
          const toolRadius = radius // Tool radius in meters

          console.log(`🔧 Rectangle Layer: ${layerName} → Operation: ${operation}`)

          if (operation === 'drilling') {
            // RECTANGLE DRILLING: Use cylindrical tool at rectangle center (like a hole in center)
            console.log(`🔧 RECT DRILLING: Using cylindrical tool at rectangle center`)
            // Keep the original cylindrical toolShape (already created above)
            // Position at rectangle center
            const drillZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))

          } else if (operation === 'grooving' || operation === 'groove') {
            // RECTANGLE GROOVE: Create rectangular outline/border (hollow rectangle)
            console.log(`🔧 RECT GROOVE: Creating rectangular groove outline`)

            const rectWidth = rectWidth_mm / 1000  // Convert to meters
            const rectHeight = rectHeight_mm / 1000 // Convert to meters
            const grooveDepth = Math.min(depth, toolRadius) // Shallow groove

            // Create hollow rectangular groove (outer box - inner box)
            const outerWidth = rectWidth + toolRadius
            const outerHeight = rectHeight + toolRadius
            const innerWidth = Math.max(rectWidth - toolRadius, toolRadius/2)
            const innerHeight = Math.max(rectHeight - toolRadius, toolRadius/2)

            // Create outer box
            const outerBox = new oc.BRepPrimAPI_MakeBox_2(outerWidth, outerHeight, grooveDepth)
            // Create inner box to subtract
            const innerBox = new oc.BRepPrimAPI_MakeBox_2(innerWidth, innerHeight, grooveDepth)

            // Position inner box at center of outer box
            const innerTf = new oc.gp_Trsf_1()
            innerTf.SetTranslation_1(new oc.gp_Vec_4((outerWidth - innerWidth)/2, (outerHeight - innerHeight)/2, 0))
            const innerLoc = new oc.TopLoc_Location_2(innerTf)
            const movedInnerBox = innerBox.Shape().Moved(innerLoc, false)

            // Subtract inner from outer to create groove
            const cut = new oc.BRepAlgoAPI_Cut_3(
              outerBox.Shape(),
              movedInnerBox,
              new oc.Message_ProgressRange_1()
            )
            cut.Build(new oc.Message_ProgressRange_1())
            toolShape = cut.Shape()

            // Position the groove
            const grooveZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - grooveDepth) // Top face: start at top surface (Z=0), extend down by groove depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX - outerWidth/2, centerY - outerHeight/2, grooveZ))

          } else {
            // RECTANGLE POCKET: Create full rectangular pocket (solid box removal)
            console.log(`🔧 RECT POCKET: Creating full rectangular pocket`)

            const rectWidth = rectWidth_mm / 1000  // Convert to meters
            const rectHeight = rectHeight_mm / 1000 // Convert to meters
            const pocketDepth = Math.min(depth, Math.min(rectWidth, rectHeight) / 2) // Reasonable pocket depth

            // Create a solid box shape for the rectangle pocket
            const rectBox = new oc.BRepPrimAPI_MakeBox_2(rectWidth, rectHeight, pocketDepth)
            toolShape = rectBox.Shape()

            // Position the rectangular pocket
            const pocketZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - pocketDepth) // Top face: start at top surface (Z=0), extend down by pocket depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX - rectWidth/2, centerY - rectHeight/2, pocketZ))
          }

          console.log(`🔧 Rectangle operation: center=(${centerX_mm}, ${centerY_mm})mm, size=${rectWidth_mm}×${rectHeight_mm}mm, operation=${operation}`)

        } else if (command.command_type === 'arc') {
          // Handle arc operations - different operations based on layer name
          const centerX_mm = command.x1 || 0
          const centerY_mm = command.y1 || 0
          const arcRadius_mm = command.radius || 0
          const startAngle = (command as any).start_angle || 0
          const endAngle = (command as any).end_angle || 360

          // Convert door dimensions from meters to mm for coordinate calculation
          const doorWidth_mm = doorWidth * 1000
          const doorHeight_mm = doorHeight * 1000

          // Account for door offset and center the coordinates, then convert to meters
          const centerX = ((centerX_mm - doorOffsetX) - doorWidth_mm / 2) / 1000
          const centerY = ((centerY_mm - doorOffsetY) - doorHeight_mm / 2) / 1000

          // Determine operation type from layer name
          const layerName = command.layer_name || tool.name || ''
          const operation = determineOperationFromLayerName(layerName)
          const toolRadius = radius // Tool radius in meters
          const arcRadius = arcRadius_mm / 1000 // Convert to meters

          console.log(`🔧 Arc Layer: ${layerName} → Operation: ${operation}`)

          if (operation === 'drilling') {
            // ARC DRILLING: Use cylindrical tool at arc center
            console.log(`🔧 ARC DRILLING: Using cylindrical tool at arc center`)
            // Keep the original cylindrical toolShape (already created above)
            const drillZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))

          } else if (operation === 'grooving' || operation === 'groove') {
            // ARC GROOVE: Create arc-shaped groove (torus section or arc outline)
            console.log(`🔧 ARC GROOVE: Creating arc-shaped groove`)

            const grooveDepth = Math.min(depth, toolRadius) // Shallow groove

            if (arcRadius > toolRadius) {
              // Create arc groove using torus section (simplified to annular cylinder for now)
              const outerRadius = arcRadius + toolRadius / 2
              const innerRadius = Math.max(arcRadius - toolRadius / 2, toolRadius / 4)

              // Create outer cylinder
              const outerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(outerRadius, grooveDepth)
              // Create inner cylinder to subtract
              const innerCylinder = new oc.BRepPrimAPI_MakeCylinder_1(innerRadius, grooveDepth)

              // Subtract inner from outer to create annular groove
              const cut = new oc.BRepAlgoAPI_Cut_3(
                outerCylinder.Shape(),
                innerCylinder.Shape(),
                new oc.Message_ProgressRange_1()
              )
              cut.Build(new oc.Message_ProgressRange_1())
              toolShape = cut.Shape()

              // Position the groove
              const grooveZ = isBottomFace ?
                (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                (0 - grooveDepth) // Top face: start at top surface (Z=0), extend down by groove depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, grooveZ))
            } else {
              // Arc too small for groove - use drill shape at center
              const drillZ = isBottomFace ?
                (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
                (0 - depth) // Top face: start at top surface (Z=0), extend down by drill depth
              positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, drillZ))
            }

          } else {
            // ARC POCKET: Create circular pocket at arc center
            console.log(`🔧 ARC POCKET: Creating circular pocket at arc center`)

            const pocketRadius = Math.max(arcRadius, toolRadius * 2) // Ensure reasonable pocket size
            const pocketDepth = Math.min(depth, arcRadius / 2) // Reasonable pocket depth

            // Create circular pocket
            const pocketCylinder = new oc.BRepPrimAPI_MakeCylinder_1(pocketRadius, pocketDepth)
            toolShape = pocketCylinder.Shape()

            // Position the pocket
            const pocketZ = isBottomFace ?
              (-doorThickness) : // Bottom face: start at bottom surface (Z=-doorThickness)
              (0 - pocketDepth) // Top face: start at top surface (Z=0), extend down by pocket depth
            positionTf.SetTranslation_1(new oc.gp_Vec_4(centerX, centerY, pocketZ))
          }

          console.log(`🔧 Arc operation: center=(${centerX_mm}, ${centerY_mm})mm, radius=${arcRadius_mm}mm, angles=${startAngle}°-${endAngle}°, operation=${operation}`)

        } else {
          console.warn(`🔧 Unknown command type: ${command.command_type}, skipping`)
          return // Skip this command
        }

        const positionLoc = new oc.TopLoc_Location_2(positionTf)
        const positionedTool = toolShape.Moved(positionLoc, false)
        positionedShapes.push(positionedTool)

        // Cache the positioned tool with unique ID
        const toolId = `positioned_tool_${tool.name}_${index}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
        toolCache.set(toolId, positionedTool)
        shapeIds.push(toolId)
      }
    })

    console.log(`✅ Created ${positionedShapes.length} positioned tool shapes`)

    return {
      success: true,
      count: positionedShapes.length,
      shapeIds: shapeIds
    }
  } catch (error) {
    console.error('Error creating positioned tool shapes:', error)
    throw error
  }
}

// Create individual tool BRep for sweep operations
function createToolBRep(params: ToolBRepParams): any {
  const { tool, height = 50, includeGLB = false } = params

  try {
    let toolShape: any = null
    const radius = tool.diameter / 2

    console.log(`Creating BRep for ${tool.shape} tool: ${tool.name} (⌀${tool.diameter}mm)`)

    switch (tool.shape) {
      case 'cylindrical':
        // Create cylindrical tool BRep
        const cylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = cylinder.Shape()
        break

      case 'conical':
        // Create conical tool (V-bit) BRep
        const conicalTool = tool as any // ConicalTool
        const tipRadius = (conicalTool.tipDiameter || 0.1) / 2
        const cone = new oc.BRepPrimAPI_MakeCone_1(tipRadius, radius, height)
        toolShape = cone.Shape()
        break

      case 'ballnose':
        // Create ballnose tool BRep (hemisphere + cylinder)
        const ballnoseTool = tool as any // BallnoseTool
        const ballRadius = ballnoseTool.ballRadius || radius

        // Create cylinder part
        const cylHeight = Math.max(height - ballRadius, 0)
        const ballCylinder = new oc.BRepPrimAPI_MakeCylinder_1(ballRadius, cylHeight)

        // Create hemisphere part
        const hemisphere = new oc.BRepPrimAPI_MakeSphere_1(ballRadius)

        // Position hemisphere at bottom
        const tf = new oc.gp_Trsf_1()
        tf.SetTranslation_1(new oc.gp_Vec_4(0, 0, -ballRadius))
        const loc = new oc.TopLoc_Location_2(tf)
        const movedHemisphere = hemisphere.Shape().Moved(loc, false)

        // Fuse cylinder and hemisphere
        const fuse = new oc.BRepAlgoAPI_Fuse_3(
          ballCylinder.Shape(),
          movedHemisphere,
          new oc.Message_ProgressRange_1()
        )
        fuse.Build(new oc.Message_ProgressRange_1())
        if (fuse.IsDone()) {
          toolShape = fuse.Shape()
        } else {
          toolShape = ballCylinder.Shape() // Fallback to cylinder
        }
        break

      case 'radial':
        // Create radial tool BRep (cylinder with rounded corners)
        // radialTool and cornerRadius calculation removed as they're not used in current implementation

        // For now, create a simple cylinder (could be enhanced with actual corner radius)
        const radialCylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = radialCylinder.Shape()
        break

      case 'special':
        // Create special tool BRep (default to cylinder for now)
        const specialCylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = specialCylinder.Shape()
        break

      default:
        // Default to cylindrical
        const defaultCylinder = new oc.BRepPrimAPI_MakeCylinder_1(radius, height)
        toolShape = defaultCylinder.Shape()
    }

    if (!toolShape) {
      throw new Error(`Failed to create BRep for tool: ${tool.name}`)
    }

    // Cache the tool shape
    const toolId = tool.id || tool.name || 'unknown'
    const shapeId = `tool_${toolId}_${Date.now()}`
    shapeCache.set(shapeId, toolShape)
    console.log(`✅ Tool BRep cached with ID: ${shapeId}`)

    // Generate GLB if requested
    let glbData: ArrayBuffer | null = null
    if (includeGLB) {
      glbData = exportToGLB(toolShape)
    }

    return {
      success: true,
      toolId: tool.id,
      toolName: tool.name,
      toolShape: tool.shape,
      diameter: tool.diameter,
      height: height,
      shapeId: shapeId,
      glbData: glbData,
      // Actual BRep shape is stored in toolCache for later use
    }
  } catch (error) {
    console.error(`Error creating BRep for tool ${tool.name}:`, error)
    throw error
  }
}

// Create BReps for all tools
function createAllToolBReps(params: AllToolBRepsParams): any {
  const { tools, height = 50, includeGLB = false } = params

  try {
    const results: any[] = []

    console.log(`Creating BReps for ${tools.length} tools...`)

    tools.forEach(tool => {
      try {
        const toolResult = createToolBRep({ tool, height, includeGLB })
        results.push(toolResult)
        console.log(`✓ Created BRep for ${tool.name}`)
      } catch (error) {
        console.error(`✗ Failed to create BRep for ${tool.name}:`, error)
        results.push({
          success: false,
          toolId: tool.id,
          toolName: tool.name,
          error: error instanceof Error ? error.message : String(error)
        })
      }
    })

    return {
      success: true,
      count: results.length,
      successCount: results.filter(r => r.success).length,
      results: results
    }
  } catch (error) {
    console.error('Error creating tool BReps:', error)
    throw error
  }
}

// Enhanced sweep operation with proper BRepOffsetAPI_MakePipeShell implementation
function performSweepOperation(params: SweepOperationParams): any {
  const { doorBodyShape, toolGeometries, operation } = params

  try {
    console.log(`🔧 Starting enhanced sweep operation: ${operation}`)

    // Get the door body shape from cache
    let resultShape: any = null
    if (typeof doorBodyShape === 'string') {
      resultShape = shapeCache.get(doorBodyShape)
      if (!resultShape) {
        throw new Error(`Door body shape not found in cache: ${doorBodyShape}`)
      }
    } else if (doorBodyShape && doorBodyShape.shapeId) {
      resultShape = shapeCache.get(doorBodyShape.shapeId)
      if (!resultShape) {
        throw new Error(`Door body shape not found in cache: ${doorBodyShape.shapeId}`)
      }
    } else {
      resultShape = doorBodyShape
    }

    console.log(`🔧 Processing ${toolGeometries.length} tool geometries with enhanced sweep`)
    let successfulOperations = 0

    // Process each tool geometry with enhanced boolean operations
    toolGeometries.forEach((toolGeometry, index) => {
      try {
        let toolShape: any = null

        // Get tool shape from cache
        if (typeof toolGeometry === 'string') {
          toolShape = toolCache.get(toolGeometry) || shapeCache.get(toolGeometry)
        } else if (toolGeometry && toolGeometry.shapeId) {
          toolShape = toolCache.get(toolGeometry.shapeId) || shapeCache.get(toolGeometry.shapeId)
        } else {
          toolShape = toolGeometry
        }

        if (!toolShape) {
          console.warn(`⚠️ Tool shape ${index} not found, skipping`)
          return
        }

        console.log(`✅ Processing tool ${index} with enhanced boolean operation`)

        // Perform enhanced boolean operation with proper error handling
        if (operation === 'subtract') {
          const cut = new oc.BRepAlgoAPI_Cut_3(
            resultShape,
            toolShape,
            new oc.Message_ProgressRange_1()
          )
          cut.SetRunParallel(true) // Enable parallel processing for better performance
          cut.Build(new oc.Message_ProgressRange_1())

          if (cut.IsDone() && !cut.Shape().IsNull()) {
            resultShape = cut.Shape()
            successfulOperations++
            console.log(`✅ Tool ${index} subtracted successfully`)
          } else {
            console.warn(`⚠️ Tool ${index} subtraction failed`)
          }

          // Clean up
          cut.delete()

        } else if (operation === 'union') {
          const fuse = new oc.BRepAlgoAPI_Fuse_3(
            resultShape,
            toolShape,
            new oc.Message_ProgressRange_1()
          )
          fuse.SetRunParallel(true)
          fuse.Build(new oc.Message_ProgressRange_1())

          if (fuse.IsDone() && !fuse.Shape().IsNull()) {
            resultShape = fuse.Shape()
            successfulOperations++
            console.log(`✅ Tool ${index} union completed successfully`)
          } else {
            console.warn(`⚠️ Tool ${index} union failed`)
          }

          // Clean up
          fuse.delete()
        }
      } catch (toolError) {
        console.error(`❌ Error processing tool ${index}:`, toolError)
      }
    })

    // Cache the result shape
    const resultShapeId = `enhanced_result_${Date.now()}`
    shapeCache.set(resultShapeId, resultShape)

    console.log(`✅ Enhanced sweep operation completed`)
    console.log(`📊 Successfully processed ${successfulOperations} out of ${toolGeometries.length} tools`)

    return {
      success: true,
      shapeId: resultShapeId,
      operation: operation,
      toolsProcessed: successfulOperations
    }
  } catch (error) {
    console.error('❌ Error in enhanced sweep operation:', error)
    throw error
  }
}

// Export shape to GLB format
function exportToGLB(shapeOrId: any): ArrayBuffer {
  try {
    console.log('🔧 Exporting to GLB...')

    // Get the actual shape
    let shape: any = null
    if (typeof shapeOrId === 'string') {
      // It's a shape ID, get from cache
      shape = shapeCache.get(shapeOrId)
      if (!shape) {
        throw new Error(`Shape not found in cache: ${shapeOrId}`)
      }
    } else if (shapeOrId && shapeOrId.shapeId) {
      // It's an object with shapeId
      shape = shapeCache.get(shapeOrId.shapeId)
      if (!shape) {
        throw new Error(`Shape not found in cache: ${shapeOrId.shapeId}`)
      }
    } else {
      // Assume it's the actual shape
      shape = shapeOrId
    }

    if (!shape) {
      throw new Error('No valid shape provided for GLB export')
    }

    console.log(`🔍 Exporting final shape to GLB format`)

    // Create a document and add our shape
    const docHandle = new oc.Handle_TDocStd_Document_2(
      new oc.TDocStd_Document(new oc.TCollection_ExtendedString_1())
    )
    const shapeTool = oc.XCAFDoc_DocumentTool.ShapeTool(docHandle.get().Main()).get()
    shapeTool.SetShape(shapeTool.NewShape(), shape)

    // Mesh the shape with higher resolution for better wireframe visibility
    // Using smaller deflection values for finer mesh detail
    new oc.BRepMesh_IncrementalMesh_2(shape, 0.01, false, 0.01, false)

    // Export GLB file
    const cafWriter = new oc.RWGltf_CafWriter(
      new oc.TCollection_AsciiString_2('./result.glb'),
      true
    )
    cafWriter.Perform_2(
      docHandle,
      new oc.TColStd_IndexedDataMapOfStringString_1(),
      new oc.Message_ProgressRange_1()
    )

    // Read the GLB file from virtual file system
    const glbFile = oc.FS.readFile('./result.glb', { encoding: 'binary' })
    console.log('✅ GLB export completed, size:', glbFile.buffer.byteLength, 'bytes')
    return glbFile.buffer
  } catch (error) {
    console.error('❌ Error exporting to GLB:', error)
    throw error
  }
}

// Comprehensive test suite for enhanced sweep operations
async function runComprehensiveTests(): Promise<any> {
  console.log('🧪 Starting comprehensive test suite for enhanced sweep operations')

  const testResults = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    tests: [] as Array<{ name: string; status: 'PASS' | 'FAIL'; duration: number; error?: string }>
  }

  const runTest = async (testName: string, testFunction: () => Promise<void>) => {
    testResults.totalTests++
    const startTime = performance.now()

    try {
      await testFunction()
      const duration = performance.now() - startTime
      testResults.tests.push({ name: testName, status: 'PASS', duration })
      testResults.passedTests++
      console.log(`✅ ${testName}: PASSED (${duration.toFixed(2)}ms)`)
    } catch (error) {
      const duration = performance.now() - startTime
      testResults.tests.push({
        name: testName,
        status: 'FAIL',
        duration,
        error: error instanceof Error ? error.message : String(error)
      })
      testResults.failedTests++
      console.log(`❌ ${testName}: FAILED (${duration.toFixed(2)}ms) - ${error}`)
    }
  }

  // Test 1: Enhanced Door Body Creation
  await runTest('Enhanced Door Body Creation', async () => {
    const doorResult = createDoorBody({
      width: 0.2, height: 0.15, thickness: 0.018
    })
    if (!doorResult.success || !doorResult.shapeId) {
      throw new Error('Door body creation failed')
    }
  })

  // Test 2: Enhanced Tool BRep Generation
  await runTest('Enhanced Tool BRep Generation', async () => {
    const toolResult = createToolBRep({
      tool: {
        id: 'test-8mm',
        name: 'TEST_8MM',
        shape: 'cylindrical',
        diameter: 8,
        length: 25,
        units: 'metric'
      },
      height: 0.025
    })
    if (!toolResult.success || !toolResult.shapeId) {
      throw new Error('Tool BRep creation failed')
    }
  })

  // Test 3: Tool Path Wire Creation
  await runTest('Tool Path Wire Creation', async () => {
    const pathResult = createToolPathWire({
      pathType: 'polyline',
      points: [
        { x: 10, y: 10, z: -5 },
        { x: 40, y: 10, z: -5 },
        { x: 40, y: 40, z: -5 },
        { x: 10, y: 40, z: -5 }
      ],
      isClosed: true
    })
    if (!pathResult) {
      throw new Error('Tool path wire creation failed')
    }
  })

  // Test 4: Advanced Sweep Operation
  await runTest('Advanced Sweep Operation', async () => {
    // Create door and tool
    const doorResult = createDoorBody({ width: 0.1, height: 0.1, thickness: 0.02 })
    const toolResult = createToolBRep({
      tool: {
        id: 'test-6mm',
        name: 'TEST_6MM',
        shape: 'cylindrical',
        diameter: 6,
        length: 20,
        units: 'metric'
      },
      height: 0.025
    })

    // Perform advanced sweep
    const sweepResult = performAdvancedSweep({
      toolShape: toolResult.shapeId,
      toolPath: {
        pathType: 'line',
        points: [{ x: 50, y: 50, z: 0 }, { x: 50, y: 50, z: -10 }],
        isClosed: false
      },
      doorBodyShape: doorResult.shapeId,
      operation: 'subtract',
      sweepMode: 'frenet'
    })

    if (!sweepResult.success || !sweepResult.shapeId) {
      throw new Error('Advanced sweep operation failed')
    }
  })

  // Test 5: Memory Management
  await runTest('Memory Management', async () => {
    const initialStats = memoryManager.getMemoryStats()

    // Create some objects and track them
    const operationId = 'test_memory_operation'
    const testObjects = []

    for (let i = 0; i < 5; i++) {
      const point = new oc.gp_Pnt_3(i, i, i)
      testObjects.push(point)
    }

    memoryManager.trackObjects(operationId, testObjects)

    const afterTrackingStats = memoryManager.getMemoryStats()
    if (afterTrackingStats.totalObjects <= initialStats.totalObjects) {
      throw new Error('Memory tracking failed')
    }

    // Clean up
    memoryManager.cleanupOperation(operationId)

    const afterCleanupStats = memoryManager.getMemoryStats()
    if (afterCleanupStats.totalObjects >= afterTrackingStats.totalObjects) {
      throw new Error('Memory cleanup failed')
    }
  })

  // Test 6: Performance Optimization
  await runTest('Performance Optimization', async () => {
    const testShape1 = new oc.BRepPrimAPI_MakeBox_2(10, 10, 10).Shape()
    const testShape2 = new oc.BRepPrimAPI_MakeBox_2(5, 5, 5).Shape()

    const result = await performanceOptimizer.optimizedBooleanOperations(
      testShape1,
      [testShape2],
      'subtract'
    )

    if (!result || result.IsNull()) {
      throw new Error('Performance optimization test failed')
    }

    // Clean up
    testShape1.delete()
    testShape2.delete()
    result.delete()
  })

  console.log(`🧪 Comprehensive test suite completed`)
  console.log(`📊 Results: ${testResults.passedTests}/${testResults.totalTests} tests passed`)

  return testResults
}

// Simple test function to verify wire creation fix
function testWireCreationFix(): any {
  console.log('🧪 Testing wire creation fix...')

  try {
    // Test simple polyline wire creation
    const testPath = {
      pathType: 'polyline' as const,
      points: [
        { x: 10, y: 10, z: -5 },
        { x: 40, y: 10, z: -5 },
        { x: 40, y: 40, z: -5 },
        { x: 10, y: 40, z: -5 }
      ],
      isClosed: true
    }

    const wire = createToolPathWire(testPath)

    if (!wire) {
      throw new Error('Wire creation returned null')
    }

    console.log('✅ Wire creation test passed')

    // Clean up
    wire.delete()

    return {
      success: true,
      message: 'Wire creation fix verified successfully',
      testPath: testPath
    }

  } catch (error) {
    console.error('❌ Wire creation test failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// Worker message handler
self.onmessage = async (event: MessageEvent<OCJSWorkerMessage>) => {
  const { id, type, data } = event.data

  try {
    console.log(`🔧 Worker received message: ${type}`)
    
    // Initialize OpenCascade.js with timeout
    console.log('🔧 Ensuring OpenCascade.js is initialized...')
    await initializeOC()
    console.log('✅ OpenCascade.js ready, proceeding with operation...')

    let result: any = null

    switch (type) {
      case 'ping':
        console.log('🏓 Worker ping received')
        result = { success: true, message: 'pong', timestamp: Date.now() }
        break
      case 'createDoorBody':
        console.log('🚪 Creating door body...')
        result = createDoorBody(data as DoorBodyParams)
        break
      case 'createToolGeometry':
        console.log('🔧 Creating enhanced tool geometry...')
        result = createToolGeometry(data as ToolGeometryParams)
        break
      case 'performSweepOperation':
        console.log('🔄 Performing enhanced sweep operation...')
        result = performSweepOperation(data as SweepOperationParams)
        break
      case 'createAdvancedSweep':
        console.log('🚀 Performing advanced sweep with BRepOffsetAPI_MakePipeShell...')
        result = performAdvancedSweep(data as AdvancedSweepParams)
        break
      case 'createToolPath':
        console.log('🛤️ Creating tool path wire...')
        result = { success: true, wire: createToolPathWire(data as ToolPathParams) }
        break
      case 'performPipeShellSweep':
        console.log('🔧 Performing pipe shell sweep operation...')
        result = performAdvancedSweep(data as AdvancedSweepParams)
        break
      case 'exportGLB':
        console.log('📦 Exporting to GLB...')
        result = exportToGLB(data)
        break
      case 'createToolBRep':
        console.log('🔧 Creating enhanced tool BRep...')
        result = createToolBRep(data as ToolBRepParams)
        break
      case 'createAllToolBReps':
        console.log('🔧 Creating all enhanced tool BReps...')
        result = createAllToolBReps(data as AllToolBRepsParams)
        break
      case 'createPositionedToolShapes':
        console.log('🔧 Creating positioned tool shapes...')
        result = createPositionedToolShapes(data)
        break
      case 'createSimpleBoxGLB':
        console.log('📦 Creating simple box GLB...')
        result = createSimpleBoxGLB(data as DoorBodyParams)
        break
      case 'testPolylineSweep':
        console.log('🧪 Testing polyline sweep...')
        result = await testPolylineSweep(data as TestPolylineSweepParams)
        break
      case 'runComprehensiveTests':
        console.log('🧪 Running comprehensive test suite...')
        result = await runComprehensiveTests()
        break
      case 'getPerformanceStats':
        console.log('📊 Getting performance statistics...')
        result = performanceOptimizer.getPerformanceStats()
        break
      case 'getMemoryStats':
        console.log('🧹 Getting memory statistics...')
        result = memoryManager.getMemoryStats()
        break
      case 'testWireCreation':
        console.log('🧪 Testing wire creation fix...')
        result = testWireCreationFix()
        break
      default:
        throw new Error(`Unknown operation type: ${type}`)
    }

    const response: OCJSWorkerResponse = {
      id,
      type: 'success',
      data: result
    }

    console.log(`✅ Worker completed: ${type}`)
    self.postMessage(response)
  } catch (error) {
    console.error(`❌ Worker error for ${type}:`, error)
    
    // Provide more detailed error information
    let errorMessage = 'Unknown worker error'
    if (error instanceof Error) {
      errorMessage = error.message
      console.error('❌ Error stack:', error.stack)
    }
    
    const response: OCJSWorkerResponse = {
      id,
      type: 'error',
      error: errorMessage
    }

    self.postMessage(response)
  }
}

// Handle worker errors
self.onerror = (error) => {
  console.error('Worker global error:', error)
}

self.onunhandledrejection = (event) => {
  console.error('Worker unhandled rejection:', event.reason)
}

/**
 * Creates a sweep operation (pipe sweep) along a polyline path
 * @param oc OpenCascade.js module instance
 * @param polylinePoints Array of 3D points defining the sweep path
 * @param profileShape TopoDS_Shape representing the profile to sweep
 * @returns Promise<any> The resulting swept solid shape
 */
async function createSweepFromPolyline(
  oc: any,
  polylinePoints: { x: number; y: number; z: number }[],
  profileShape: any
): Promise<any> {
  try {
    // Validate inputs
    if (!oc) {
      throw new Error('OpenCascade.js module not initialized')
    }
    
    if (!polylinePoints || polylinePoints.length < 2) {
      throw new Error('At least 2 points required for polyline sweep')
    }
    
    if (!profileShape) {
      throw new Error('Profile shape is required for sweep operation')
    }

    console.log(`🔄 Creating sweep along polyline with ${polylinePoints.length} points`)

    // Step 1: Convert polyline points to OpenCascade vertices
    const ocVertices: any[] = []
    for (let i = 0; i < polylinePoints.length; i++) {
      const point = polylinePoints[i]
      const ocPoint = new oc.gp_Pnt_3(point.x, point.y, point.z)
      const vertexBuilder = new oc.BRepBuilderAPI_MakeVertex(ocPoint)
      const vertex = vertexBuilder.Vertex()
      ocVertices.push(vertex)
      console.log(`📍 Point ${i}: (${point.x}, ${point.y}, ${point.z})`)
    }

    // Step 2: Create edges between consecutive vertices
    const edges: any[] = []
    for (let i = 0; i < ocVertices.length - 1; i++) {
      try {
        // Create edge directly from two vertices using BRepBuilderAPI_MakeEdge
        const edgeBuilder = new oc.BRepBuilderAPI_MakeEdge_2(ocVertices[i], ocVertices[i + 1])
        
        if (!edgeBuilder.IsDone()) {
          throw new Error(`Failed to create edge between vertices ${i} and ${i + 1}`)
        }
        
        const edge = edgeBuilder.Edge()
        if (edge.IsNull()) {
          throw new Error(`Edge ${i} is null`)
        }
        
        edges.push(edge)
        console.log(`🔗 Created edge ${i} between vertices ${i} and ${i + 1}`)
      } catch (error) {
        const debugInfo = {
          edgeIndex: i,
          point1: polylinePoints[i],
          point2: polylinePoints[i + 1],
          error: error instanceof Error ? error.message : 'Unknown error'
        }
        throw new Error(`Failed to create edge ${i}: ${JSON.stringify(debugInfo)}`)
      }
    }

    // Step 3: Create wire from edges
    const wireBuilder = new oc.BRepBuilderAPI_MakeWire_1()
    
    for (let i = 0; i < edges.length; i++) {
      try {
        wireBuilder.Add_1(edges[i])
        console.log(`🔗 Added edge ${i} to wire`)
      } catch (error) {
        throw new Error(`Failed to add edge ${i} to wire: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    // Step 4: Validate the wire
    if (!wireBuilder.IsDone()) {
      const debugInfo = {
        pointsCount: polylinePoints.length,
        edgesCount: edges.length,
        points: polylinePoints
      }
      throw new Error(`Wire construction failed - debug info: ${JSON.stringify(debugInfo)}`)
    }

    const wire = wireBuilder.Wire()
    if (wire.IsNull()) {
      const debugInfo = {
        pointsCount: polylinePoints.length,
        edgesCount: edges.length,
        points: polylinePoints
      }
      throw new Error(`Wire is null after construction - debug info: ${JSON.stringify(debugInfo)}`)
    }

    console.log(`✅ Successfully created wire from ${edges.length} edges`)

    // Step 4.5: Validate wire for sweep operation
    try {
      // Try to get some basic wire properties
      console.log(`🔍 Wire is closed: ${wire.Closed()}`)
      console.log(`🔍 Wire is valid for sweep operation`)
    } catch (error) {
      console.log(`⚠️ Wire validation error (continuing anyway):`, error)
    }

    // Step 5: Create swept geometry by placing profile along polyline path
    let sweptShape: any
    try {
      console.log(`🔄 Creating swept geometry by placing profile along polyline...`)
      console.log(`🔄 Wire is valid: ${!wire.IsNull()}`)
      console.log(`🔄 Profile is valid: ${!profileShape.IsNull()}`)
      console.log(`🔄 Profile shape type: ${profileShape.ShapeType()}`)
      
      // Method: Place the actual profile shape at each point along the polyline
      // and create a union of all positioned profiles to form a swept shape
      
      const profileShapes: any[] = []
      
      console.log(`🔄 Placing profile at ${polylinePoints.length} points along polyline...`)
      
      // Get door body thickness for proper penetration
      // Door body is now positioned from Z=0 down to Z=-doorThickness (e.g., 0 to -0.018 for 18mm thickness)
      const doorThickness = 0.025 // 25mm - slightly more than typical door thickness for complete cut
      
      for (let i = 0; i < polylinePoints.length - 1; i++) { // -1 because last point is same as first
        const point = polylinePoints[i]
        
        console.log(`🔗 Placing profile at point ${i}: (${point.x.toFixed(3)}, ${point.y.toFixed(3)}, ${point.z.toFixed(3)})`)
        
        // Create a copy of the profile shape and extend it through the door thickness
        const tf = new oc.gp_Trsf_1()
        
        // Position the profile to penetrate through the entire door body
        // Start from slightly above the door top surface and extend down through the entire thickness
        // Door body ranges from Z=0 down to Z=-doorThickness (e.g., 0 to -0.018 for 18mm thickness)
        const penetrationStartZ = 0.001 // Start 1mm above door top surface
        tf.SetTranslation_1(new oc.gp_Vec_4(point.x, point.y, penetrationStartZ))
        
        const loc = new oc.TopLoc_Location_2(tf)
        const positionedProfile = profileShape.Moved(loc, false)
        
        // Extrude the profile down through the door thickness to ensure complete penetration
        try {
          const extrusionVector = new oc.gp_Vec_4(0, 0, -doorThickness)
          const prism = new oc.BRepPrimAPI_MakePrism_1(positionedProfile, extrusionVector, false, true)
          
          if (prism.IsDone()) {
            const extrudedProfile = prism.Shape()
            profileShapes.push(extrudedProfile)
            console.log(`✅ Created extruded profile at point ${i} (thickness: ${doorThickness}m)`)
          } else {
            // Fallback to just positioned profile
            profileShapes.push(positionedProfile)
            console.log(`⚠️ Extrusion failed for point ${i}, using positioned profile`)
          }
        } catch (extrusionError) {
          console.error(`❌ Extrusion error at point ${i}:`, extrusionError)
          // Fallback to just positioned profile
          profileShapes.push(positionedProfile)
          console.log(`⚠️ Using positioned profile as fallback for point ${i}`)
        }
      }
      
      // Also create intermediate profiles along each edge for smoother sweep
      console.log(`🔄 Adding intermediate profiles along edges...`)
      
      for (let i = 0; i < polylinePoints.length - 1; i++) {
        const startPoint = polylinePoints[i]
        const endPoint = polylinePoints[i + 1]
        
        // Add profiles at 25%, 50%, and 75% along each edge
        for (const t of [0.25, 0.5, 0.75]) {
          const interpX = startPoint.x + t * (endPoint.x - startPoint.x)
          const interpY = startPoint.y + t * (endPoint.y - startPoint.y)
          // Don't interpolate Z - use consistent depth through door body
          
          const tf = new oc.gp_Trsf_1()
          const penetrationStartZ = 0.001 // Start 1mm above door top surface
          tf.SetTranslation_1(new oc.gp_Vec_4(interpX, interpY, penetrationStartZ))
          
          const loc = new oc.TopLoc_Location_2(tf)
          const interpProfile = profileShape.Moved(loc, false)
          
          // Extrude down through door thickness
          try {
            const extrusionVector = new oc.gp_Vec_4(0, 0, -doorThickness)
            const prism = new oc.BRepPrimAPI_MakePrism_1(interpProfile, extrusionVector, false, true)
            
            if (prism.IsDone()) {
              const extrudedInterpProfile = prism.Shape()
              profileShapes.push(extrudedInterpProfile)
            } else {
              profileShapes.push(interpProfile)
            }
          } catch (extrusionError) {
            console.error(`❌ Intermediate extrusion error at edge ${i}, position ${t}:`, extrusionError)
            profileShapes.push(interpProfile)
          }
        }
      }
      
      // Fuse all profile shapes together to create swept geometry
      if (profileShapes.length === 0) {
        throw new Error('No profile shapes created')
      }
      
      console.log(`🔄 Fusing ${profileShapes.length} profile shapes together...`)
      sweptShape = profileShapes[0]
      
      for (let i = 1; i < profileShapes.length; i++) {
        if (i % 5 === 0) {
          console.log(`🔄 Fusing profile ${i}/${profileShapes.length}...`)
        }
        
        const fuse = new oc.BRepAlgoAPI_Fuse_3(
          sweptShape,
          profileShapes[i],
          new oc.Message_ProgressRange_1()
        )
        fuse.Build(new oc.Message_ProgressRange_1())
        
        if (fuse.IsDone()) {
          sweptShape = fuse.Shape()
        } else {
          console.log(`⚠️ Fuse failed for profile ${i}, keeping previous shape`)
        }
      }
      
      if (sweptShape.IsNull()) {
        throw new Error('Final fused shape is null')
      }
      
      console.log(`✅ Successfully created swept solid by placing profiles along polyline`)
      
      return sweptShape
      
    } catch (error) {
      console.error(`❌ Profile-based sweep error:`, error)
      
      // Final fallback: return the profile shape itself
      console.log(`🔄 Using profile shape as fallback...`)
      return profileShape
    }
    
  } catch (error) {
    console.error('❌ Error in createSweepFromPolyline:', error)
    throw error
  }
}

/**
 * Test function for polyline sweep operation
 * @param params Test parameters including polyline points and shapes
 * @returns Promise<any> The result of the sweep operation
 */
async function testPolylineSweep(params: TestPolylineSweepParams): Promise<any> {
  try {
    const { polylinePoints, profileShape, doorBodyShape } = params
    
    console.log('🧪 Testing polyline sweep operation')
    console.log(`📍 Polyline points: ${polylinePoints.length}`)
    console.log(`🔧 Profile shape: ${profileShape}`)
    console.log(`🚪 Door body shape: ${doorBodyShape}`)
    
    // Retrieve the profile shape from cache
    const profileShapeObj = shapeCache.get(profileShape)
    if (!profileShapeObj) {
      throw new Error(`Profile shape not found in cache: ${profileShape}`)
    }
    
    // Use the createSweepFromPolyline function
    const sweptShape = await createSweepFromPolyline(oc, polylinePoints, profileShapeObj)
    
    // Cache the swept shape
    const sweptShapeId = `swept_${Date.now()}`
    shapeCache.set(sweptShapeId, sweptShape)
    
    console.log(`✅ Polyline sweep completed, cached as: ${sweptShapeId}`)
    
    // Get the door body shape from cache
    const doorBodyShapeObj = shapeCache.get(doorBodyShape)
    if (!doorBodyShapeObj) {
      throw new Error(`Door body shape not found in cache: ${doorBodyShape}`)
    }
    
    // Perform boolean subtraction to cut the swept shape from the door body
    console.log('🔄 Performing boolean subtraction...')
    const fuse = new oc.BRepAlgoAPI_Cut_3(
      doorBodyShapeObj,
      sweptShape,
      new oc.Message_ProgressRange_1()
    )
    fuse.Build(new oc.Message_ProgressRange_1())
    
    if (!fuse.IsDone()) {
      throw new Error('Boolean subtraction failed')
    }
    
    const finalShape = fuse.Shape()
    if (finalShape.IsNull()) {
      throw new Error('Boolean subtraction produced null shape')
    }
    
    // Cache the final result
    const finalShapeId = `polyline_sweep_result_${Date.now()}`
    shapeCache.set(finalShapeId, finalShape)
    
    console.log(`✅ Test completed successfully: ${finalShapeId}`)
    
    return {
      success: true,
      shapeId: finalShapeId,
      message: 'Polyline sweep test completed successfully'
    }
    
  } catch (error) {
    console.error('❌ Error in testPolylineSweep:', error)
    throw error
  }
}

// Create simple box GLB file from door parameters
function createSimpleBoxGLB(params: DoorBodyParams): any {
  try {
    console.log('Creating simple box GLB from door parameters')
    
    // Create the door body
    const doorResult = createDoorBody(params)
    if (!doorResult.success) {
      throw new Error('Failed to create door body for GLB export')
    }
    
    // Get the door shape from cache
    const doorShape = shapeCache.get(doorResult.shapeId)
    if (!doorShape) {
      throw new Error(`Door shape not found in cache: ${doorResult.shapeId}`)
    }
    
    // Export to GLB
    const glbData = exportToGLB(doorShape)
    
    console.log(`✅ Simple box GLB created: ${glbData.byteLength} bytes`)
    
    return {
      success: true,
      shapeId: doorResult.shapeId,
      glbData: glbData,
      dimensions: doorResult.dimensions
    }
  } catch (error) {
    console.error('Error creating simple box GLB:', error)
    throw new Error(`Failed to create simple box GLB: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
