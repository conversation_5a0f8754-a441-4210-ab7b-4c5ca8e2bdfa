# Enhanced 3D Visualization System - Developer Usage Guide

## Quick Start

### 1. Basic Setup

```typescript
import { ocjsService } from '@/services/ocjsService'
import { cncToolService } from '@/services/cncToolService'

// Initialize the enhanced system
await ocjsService.initialize()
```

### 2. Create Enhanced Door Body

```typescript
const doorParams = {
  width: 0.2,      // 200mm in meters
  height: 0.15,    // 150mm in meters  
  thickness: 0.018, // 18mm in meters
  cornerRadius: 0.005 // 5mm corner radius (optional)
}

const doorResult = await ocjsService.createDoorBody(doorParams)
console.log('Door created:', doorResult.shapeId)
```

### 3. Create Enhanced Tools

```typescript
// Get enhanced tool from service
const enhancedTool = cncToolService.getEnhancedTool('enhanced-cyl-8mm')

// Or create custom tool
const customTool = {
  id: 'custom-tool',
  name: 'Custom 6MM Endmill',
  shape: 'cylindrical',
  diameter: 6,
  length: 25,
  units: 'metric'
}

const toolResult = await ocjsService.sendMessage('createToolBRep', {
  tool: customTool,
  height: 0.025 // 25mm tool height in meters
})
```

### 4. Advanced Sweep Operations

```typescript
// Define tool path
const toolPath = {
  pathType: 'polyline',
  points: [
    { x: 20, y: 20, z: -5 },   // Start point
    { x: 180, y: 20, z: -5 },  // Move right
    { x: 180, y: 130, z: -5 }, // Move down
    { x: 20, y: 130, z: -5 }   // Move left
  ],
  isClosed: true // Close the path for pocket operation
}

// Perform advanced sweep with BRepOffsetAPI_MakePipeShell
const sweepResult = await ocjsService.performAdvancedSweep({
  toolShape: toolResult.shapeId,
  toolPath: toolPath,
  doorBodyShape: doorResult.shapeId,
  operation: 'subtract',
  sweepMode: 'frenet'
})
```

## Advanced Usage

### Enhanced Tool Path Generation

```typescript
// Create tool paths from draw commands
const commands = [
  {
    command_type: 'circle',
    x1: 100, y1: 75,
    radius: 15,
    layer_name: 'CEP_8MM_POCKET'
  }
]

const toolPaths = cncToolService.createEnhancedToolPath(
  commands,
  enhancedTool,
  5, // 5mm depth
  'pocket'
)
```

### Memory Management

```typescript
// Memory is automatically managed, but you can monitor it
const memStats = await ocjsService.sendMessage('getMemoryStats', {})
console.log('Memory usage:', memStats)

// Manual cleanup for specific operations (optional)
const operationId = 'my-operation'
memoryManager.trackObjects(operationId, [/* objects */])
memoryManager.cleanupOperation(operationId)
```

### Performance Monitoring

```typescript
// Get performance statistics
const perfStats = await ocjsService.sendMessage('getPerformanceStats', {})
console.log('Performance stats:', perfStats)

// Time custom operations
const result = performanceOptimizer.timeOperation('MyOperation', () => {
  // Your operation here
  return someResult
})
```

## Tool Types and Operations

### Cylindrical Tools (Endmills)

```typescript
const cylindricalTool = {
  id: 'cyl-8mm',
  name: '8MM Endmill',
  shape: 'cylindrical',
  diameter: 8,
  length: 25,
  units: 'metric'
}

// Best for: Pocketing, profiling, slotting
```

### Ballnose Tools

```typescript
const ballnoseTool = {
  id: 'ball-6mm',
  name: '6MM Ballnose',
  shape: 'ballnose',
  diameter: 6,
  length: 20,
  ballRadius: 3, // Required for ballnose
  units: 'metric'
}

// Best for: 3D contouring, finishing operations
```

### Conical Tools (V-bits)

```typescript
const conicalTool = {
  id: 'v-90deg',
  name: '90° V-bit',
  shape: 'conical',
  diameter: 12, // Cutting diameter
  length: 15,
  tipAngle: 90, // V-angle
  units: 'metric'
}

// Best for: V-carving, chamfering, engraving
```

## Operation Types

### Drilling Operations

```typescript
const drillPath = {
  pathType: 'line',
  points: [
    { x: 50, y: 50, z: 0 },    // Surface
    { x: 50, y: 50, z: -10 }   // Drill depth
  ],
  isClosed: false
}
```

### Pocket Operations

```typescript
const pocketPath = {
  pathType: 'polyline',
  points: [
    // Rectangular pocket outline
    { x: 20, y: 20, z: -5 },
    { x: 80, y: 20, z: -5 },
    { x: 80, y: 60, z: -5 },
    { x: 20, y: 60, z: -5 }
  ],
  isClosed: true
}
```

### Groove Operations

```typescript
const groovePath = {
  pathType: 'polyline',
  points: [
    // Groove centerline
    { x: 10, y: 50, z: -3 },
    { x: 190, y: 50, z: -3 }
  ],
  isClosed: false
}
```

## Sweep Modes

### Frenet Mode (Default)
```typescript
sweepMode: 'frenet'
// Tool follows natural curve orientation
// Best for: Most general applications
```

### Corrected Frenet Mode
```typescript
sweepMode: 'corrected_frenet'
// Tool maintains Z-up orientation
// Best for: Vertical walls, consistent orientation
```

### Fixed Mode
```typescript
sweepMode: 'fixed'
// Tool maintains fixed orientation
// Best for: Simple linear operations
```

## Error Handling

### Common Error Patterns

```typescript
try {
  const result = await ocjsService.performAdvancedSweep(params)
  
  if (!result.success) {
    throw new Error(`Sweep operation failed: ${result.error}`)
  }
  
} catch (error) {
  console.error('Sweep operation error:', error)
  
  // Check specific error types
  if (error.message.includes('Tool path')) {
    // Handle tool path errors
  } else if (error.message.includes('Boolean')) {
    // Handle boolean operation errors
  }
}
```

### Validation

```typescript
// Validate tool path before processing
function validateToolPath(toolPath) {
  if (!toolPath.points || toolPath.points.length < 2) {
    throw new Error('Tool path must have at least 2 points')
  }
  
  for (const point of toolPath.points) {
    if (typeof point.x !== 'number' || typeof point.y !== 'number' || typeof point.z !== 'number') {
      throw new Error('All tool path points must have numeric x, y, z coordinates')
    }
  }
}
```

## Testing Your Implementation

### Unit Testing

```typescript
// Test enhanced door creation
describe('Enhanced Door Creation', () => {
  it('should create door with proper dimensions', async () => {
    const result = await ocjsService.createDoorBody({
      width: 0.1, height: 0.1, thickness: 0.02
    })
    
    expect(result.success).toBe(true)
    expect(result.shapeId).toBeDefined()
  })
})

// Test tool creation
describe('Enhanced Tool Creation', () => {
  it('should create cylindrical tool BRep', async () => {
    const result = await ocjsService.sendMessage('createToolBRep', {
      tool: { id: 'test', name: 'Test', shape: 'cylindrical', diameter: 8, length: 25, units: 'metric' },
      height: 0.025
    })
    
    expect(result.success).toBe(true)
    expect(result.shapeId).toBeDefined()
  })
})
```

### Integration Testing

```typescript
// Test complete workflow
describe('Complete CAM Workflow', () => {
  it('should process door with tools successfully', async () => {
    const doorParams = { width: 0.2, height: 0.15, thickness: 0.018 }
    const toolOperations = [{
      tool: enhancedTool,
      commands: testCommands,
      depth: 5,
      isBottomFace: false,
      operationType: 'pocket'
    }]
    
    const result = await ocjsService.processDoorWithAdvancedSweep(
      doorParams,
      toolOperations
    )
    
    expect(result).toBeInstanceOf(ArrayBuffer)
    expect(result.byteLength).toBeGreaterThan(0)
  })
})
```

### Performance Testing

```typescript
// Test performance with large datasets
describe('Performance Tests', () => {
  it('should handle large tool sets efficiently', async () => {
    const startTime = performance.now()
    
    // Create many tools
    const tools = Array.from({ length: 100 }, (_, i) => createTestTool(i))
    
    const result = await processLargeToolSet(tools)
    
    const duration = performance.now() - startTime
    expect(duration).toBeLessThan(10000) // Should complete in under 10 seconds
  })
})
```

## Best Practices

### 1. Coordinate System
- Always use meters as the base unit for OCJS operations
- Convert millimeter inputs to meters: `mm / 1000`
- Maintain consistent coordinate systems throughout

### 2. Memory Management
- Let the system handle memory automatically
- Monitor memory usage in production
- Use operation IDs for organized cleanup

### 3. Performance
- Use batch processing for large tool sets
- Enable parallel processing for boolean operations
- Optimize tool paths before processing
- Monitor operation timing

### 4. Error Handling
- Always validate inputs before processing
- Handle specific error types appropriately
- Provide meaningful error messages to users
- Log errors for debugging

### 5. Testing
- Test with various tool types and sizes
- Validate with different door dimensions
- Test edge cases and error conditions
- Monitor performance with realistic datasets
